using System.Collections.Generic;
using System.Linq;
using ConcurrentCollections;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(MapNode))]
  [FriendOf(typeof(MapNode))]
  public static partial class MapNodeSystem
  {
    [EntitySystem]
    public static void Awake(this MapNode self)
    {
    }

    public static string GetMapNodeQueryKey(this MapNode self)
    {
      return self.mapName + "_" + self.pointName;
    }

    public static bool ContainsNpc(this MapNode self, NpcNameEnum npcName)
    {
      foreach (var npc in self.npcInPoint)
      {
        if (npc.Value.name == npcName)
        {
          return true;
        }
      }
      return false;
    }

    public static void GetMapName(this MapNode self, out string mapName, out string pointName)
    {
      mapName = self.mapName;
      pointName = self.pointName;
    }

    public static MapDaoInfo GetMapDaoInfo(this MapNode self)
    {
      MapDaoInfo mapDaoInfo = new()
      {
        mapName = self.mapName,
        pointName = self.pointName,
        nodeType = self.nodeType,
        nodeStates = self.nodeStates.ToList(),
        userInPoint = self.userInPoint,
        npcInPoint = self.npcInPoint,
        monInPoint = self.monInPoint
      };
      return mapDaoInfo;
    }

    public static void ParseMapDaoInfo(this MapNode self, MapDaoInfo mapDaoInfo)
    {
      self.mapName = mapDaoInfo.mapName;
      self.pointName = mapDaoInfo.pointName;
      self.nodeType = mapDaoInfo.nodeType;
      self.nodeStates = new ConcurrentHashSet<MapNodeState>(mapDaoInfo.nodeStates);
      self.userInPoint = mapDaoInfo.userInPoint;
      self.npcInPoint = mapDaoInfo.npcInPoint;
      self.monInPoint = mapDaoInfo.monInPoint;
    }
  }
}