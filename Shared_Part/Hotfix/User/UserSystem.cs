using System.Linq;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(User))]
  [FriendOf(typeof(User))]
  [FriendOf(typeof(AttackComponent))]
  [FriendOf(typeof(MoveComponent))]
  public static partial class UserSystem
  {
    [EntitySystem]
    private static void Awake(this User self)
    {
    }

    public static UserInMap GetUserInMap(this User self)
    {
      AttackComponent attackComponent = self.GetComponent<AttackComponent>();
      UserInMap userInMap = new()
      {
        id = self.Id,
        name = self.nickname,
        offlineState = self.offlineState,
        liveState = attackComponent.LiveState,
        userStates = self.userStates.Keys.ToList()
      };
      return userInMap;
    }

    public static bool HasUserState(this User self, UserStateEnum userState)
    {
      return self.userStates.ContainsKey(userState);
    }

    public static UserStateDetail GetUserState(this User self, UserStateEnum userState)
    {
      self.userStates.TryGetValue(userState, out UserStateDetail userStateDetail);
      return userStateDetail;
    }

    public static UserBaseDaoInfo GetUserBaseDaoInfo(this User self)
    {
      MoveComponent moveComponent = self.GetComponent<MoveComponent>();
      return new UserBaseDaoInfo
      {
        id = self.Id,
        netAccountId = self.netAccountId,
        name = self.nickname,
        userType = self.userType,
        teamInfo = self.teamInfo,
        userComInfo = self.userComInfo,
        userStates = self.userStates,
        offlineState = self.offlineState,
        evilNum = self.evilNum,
        nowMap = moveComponent.nowMap,
        nowPoint = moveComponent.nowPoint,
        stepLimit = moveComponent.stepLimit
      };
    }

    public static void ParseUserBaseDaoInfo(this User self, UserBaseDaoInfo userBaseDaoInfo)
    {
      self.Id = userBaseDaoInfo.id;
      self.netAccountId = userBaseDaoInfo.netAccountId;
      self.nickname = userBaseDaoInfo.name;
      self.userType = userBaseDaoInfo.userType;
      self.teamInfo = userBaseDaoInfo.teamInfo;
      self.userComInfo = userBaseDaoInfo.userComInfo;
      self.userStates = userBaseDaoInfo.userStates;
      self.offlineState = userBaseDaoInfo.offlineState;
      self.evilNum = userBaseDaoInfo.evilNum;
      self.RemoveComponent<MoveComponent>();
      self.AddComponent<MoveComponent, string, string, int>(userBaseDaoInfo.nowMap, userBaseDaoInfo.nowPoint, userBaseDaoInfo.stepLimit);
    }
  }
}