using System.Linq;

namespace MaoYouJi
{
  /// <summary>
  /// 公会信息系统 - 共享层
  /// </summary>
  [EntitySystemOf(typeof(GuildInfo))]
  [FriendOf(typeof(GuildInfo))]
  public static partial class GuildInfoSystem
  {
    [EntitySystem]
    private static void Awake(this GuildInfo self)
    {
    }

    [EntitySystem]
    private static void Awake(this GuildInfo self, string guildName, User creator)
    {
      self.name = guildName;
      self.description = "会长很懒，什么都没写";
      self.announcement = "欢迎加入公会！";
      self.leaderId = creator.Id;
      self.level = 1;
      self.maxMembers = 50;
      self.createTime = TimeInfo.Instance.ServerNow();
      self.updateTime = self.createTime;

      // 添加创建者为会长
      GuildMemberInfo leaderInfo = new GuildMemberInfo
      {
        userId = creator.Id,
        name = creator.nickname,
        role = GuildRole.President,
        joinTime = self.createTime,
        level = (int)creator.GetComponent<AttackComponent>().level,
        attackNum = creator.GetComponent<AttackComponent>().attackNum,
        offlineState = creator.offlineState,
        lastActiveTime = self.createTime,
        contribution = "创建者"
      };

      self.memberInfos.TryAdd(creator.Id, leaderInfo);
    }

    [EntitySystem]
    private static void Destroy(this GuildInfo self)
    {
      self.memberInfosLock?.Dispose();
      self.allianceInfosLock?.Dispose();
    }

    /// <summary>
    /// 转换为数据传输对象
    /// </summary>
    public static GuildDaoInfo ToDaoInfo(this GuildInfo self)
    {
      return new GuildDaoInfo
      {
        guildId = self.Id,
        name = self.name,
        description = self.description,
        announcement = self.announcement,
        leaderId = self.leaderId,
        level = self.level,
        maxMembers = self.maxMembers,
        createTime = self.createTime,
        updateTime = self.updateTime,
        memberInfos = self.memberInfos.ToDictionary(x => x.Key, x => x.Value),
        allianceInfos = self.allianceInfos.ToDictionary(x => x.Key, x => x.Value)
      };
    }

    /// <summary>
    /// 转换为简化公会信息
    /// </summary>
    public static SimpleGuildInfo ToSimpleInfo(this GuildInfo self)
    {
      string leaderName = "";
      if (self.memberInfos.TryGetValue(self.leaderId, out GuildMemberInfo leaderInfo))
      {
        leaderName = leaderInfo.name;
      }

      return new SimpleGuildInfo
      {
        guildId = self.Id,
        name = self.name,
        description = self.description,
        level = self.level,
        memberCount = self.memberInfos.Count,
        maxMembers = self.maxMembers,
        leaderName = leaderName,
        createTime = self.createTime
      };
    }

    /// <summary>
    /// 获取成员角色
    /// </summary>
    public static GuildRole GetMemberRole(this GuildInfo self, long userId)
    {
      if (self.memberInfos.TryGetValue(userId, out GuildMemberInfo memberInfo))
      {
        return memberInfo.role;
      }
      return GuildRole.Member; // 默认返回会员角色
    }

    /// <summary>
    /// 检查用户是否是公会成员
    /// </summary>
    public static bool IsMember(this GuildInfo self, long userId)
    {
      return self.memberInfos.ContainsKey(userId);
    }

    /// <summary>
    /// 检查是否已达到最大成员数
    /// </summary>
    public static bool IsMaxMembers(this GuildInfo self)
    {
      return self.memberInfos.Count >= self.maxMembers;
    }

    /// <summary>
    /// 获取在线成员数量
    /// </summary>
    public static int GetOnlineMemberCount(this GuildInfo self)
    {
      return self.memberInfos.Values.Count(m => m.offlineState == OfflineStateEnum.ONLINE);
    }

    /// <summary>
    /// 更新成员信息
    /// </summary>
    public static void UpdateMemberInfo(this GuildInfo self, User user)
    {
      if (!self.memberInfos.TryGetValue(user.Id, out GuildMemberInfo memberInfo))
      {
        return;
      }

      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      memberInfo.name = user.nickname;
      memberInfo.level = (int)attackComponent.level;
      memberInfo.attackNum = attackComponent.attackNum;
      memberInfo.offlineState = user.offlineState;
      memberInfo.lastActiveTime = TimeInfo.Instance.ServerNow();

      self.updateTime = TimeInfo.Instance.ServerNow();
    }
  }
}
