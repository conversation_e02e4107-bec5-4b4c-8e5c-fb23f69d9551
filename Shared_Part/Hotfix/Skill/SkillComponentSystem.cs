using System.Linq;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(SkillComponent))]
  [FriendOf(typeof(SkillComponent))]
  public static partial class SkillComponentSystem
  {
    [EntitySystem]
    private static void Awake(this SkillComponent self)
    {
    }

    public static SkillComponentDaoInfo ToDaoInfo(this SkillComponent self)
    {
      SkillComponentDaoInfo skillComponentDaoInfo = new();
      skillComponentDaoInfo.skillMap = new(self.skillMap);
      skillComponentDaoInfo.skillIds = self.skillIds;
      skillComponentDaoInfo.equipSkills = self.equipSkills;
      skillComponentDaoInfo.skillCool = new(self.skillCool);
      skillComponentDaoInfo.quickFoods = self.quickFoods;
      return skillComponentDaoInfo;
    }

    public static void ParseFromDaoInfo(this SkillComponent self, SkillComponentDaoInfo daoInf)
    {
      self.skillMap = new(daoInf.skillMap);
      self.skillIds = daoInf.skillIds;
      self.equipSkills = daoInf.equipSkills;
      self.skillCool = new(daoInf.skillCool);
      self.quickFoods = daoInf.quickFoods;
    }

    // 获取技能信息
    public static Skill GetSkill(this SkillComponent self, SkillIdEnum skillNameEnum, bool checkEquip = false)
    {
      if (self.skillMap == null)
      {
        return null;
      }
      self.skillMap.TryGetValue(skillNameEnum, out Skill skill);
      if (skill != null && checkEquip)
      {
        if (!self.CheckSkillEquip(skillNameEnum))
        {
          return null;
        }
      }
      return skill;
    }

    public static bool CheckSkillEquip(this SkillComponent self, SkillIdEnum skillNameEnum)
    {
      return self.equipSkills.Any(equipSkill => equipSkill == skillNameEnum);
    }

    public static Skill GetNowTalentSkill(this SkillComponent self)
    {
      foreach (Skill nowSkill in self.skillMap.Values)
      {
        if (nowSkill.skillType == SkillTypeEnum.TALENT_SKILL && nowSkill.isActive)
        {
          return nowSkill;
        }
      }
      return null;
    }

    public static MaoJob GetNowMaoJob(this SkillComponent self)
    {
      foreach (Skill skill in self.skillMap.Values)
      {
        if (skill.skillType == SkillTypeEnum.JOB_Base_SKILL && skill.isActive)
        {
          return skill.job;
        }
      }
      return MaoJob.None;
    }

    public static Skill GetNowBaseJobSkill(this SkillComponent self)
    {
      foreach (Skill nowSkill in self.skillMap.Values)
      {
        if (nowSkill.skillType == SkillTypeEnum.JOB_Base_SKILL && nowSkill.isActive)
        {
          return nowSkill;
        }
      }
      return null;
    }
  }
}