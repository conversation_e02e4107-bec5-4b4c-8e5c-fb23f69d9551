namespace MaoYouJi
{
  public static class SkillComponentProc
  {
    /**
    * 计算技能等级经验，适用于通用职业技能
    * 
    * @param level 等级
    * @return 经验
    */
    public static int calcLevelExp(int level)
    {
      if (level == 1)
      {
        return 20;
      }
      int a1 = 20;
      int currentValue = a1;
      int d = 20;

      for (int i = 2; i <= level; i++)
      {
        if (i > 2 && i < 30)
        {
          d += 5;
        }
        else if ((i - 1) % 2 == 0)
        {
          d += 5;
        }
        currentValue += d;
      }
      return currentValue;
    }
  }
}
