using System.Collections.Concurrent;
using System.Collections.Generic;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(AttackComponent))]
  [FriendOf(typeof(AttackComponent))]
  public static partial class AttackComponentSystem
  {
    [EntitySystem]
    private static void Awake(this AttackComponent self, FightInfo fightInfo)
    {
      self.fightInfo = fightInfo;
    }

    public static AttackDaoInfo GetAttackDaoInfo(this AttackComponent self)
    {
      AttackDaoInfo attackDaoInfo = self.GetSimpleAttackDaoInfo();
      SkillComponent skillComponent = self.GetComponent<SkillComponent>();
      if (skillComponent != null)
      {
        attackDaoInfo.skillIds = new(skillComponent.skillIds);
        attackDaoInfo.skillMap = new Dictionary<SkillIdEnum, Skill>(skillComponent.skillMap);
        attackDaoInfo.equipSkills = skillComponent.equipSkills;
        attackDaoInfo.quickFoods = skillComponent.quickFoods;
        attackDaoInfo.skillCool = new Dictionary<SkillIdEnum, long>(skillComponent.skillCool);
      }
      User user = self.GetParent<User>();
      if (user != null)
      {
        EquipComponent equipComponent = user.GetComponent<EquipComponent>();
        if (equipComponent != null && equipComponent.InstanceId != 0)
        {
          attackDaoInfo.equipList = equipComponent.equipList;
          attackDaoInfo.specialEquipList = equipComponent.specialEquipList;
        }
      }
      return attackDaoInfo;
    }

    public static AttackDaoInfo GetSimpleAttackDaoInfo(this AttackComponent self)
    {
      AttackDaoInfo attackDaoInfo = new AttackDaoInfo
      {
        fightInfo = self.fightInfo,
        job = self.job,
        maxBlood = self.maxBlood,
        maxBlue = self.maxBlue,
        minAttack = self.minAttack,
        maxAttack = self.maxAttack,
        realAttack = self.realAttack,
        attackRate = self.attackRate,
        crit = self.crit,
        critDmg = self.critDmg,
        hitRate = self.hitRate,
        miss = self.miss,
        defense = self.defense,
        magicDefense = self.magicDefense,
        strength = self.strength,
        power = self.power,
        quick = self.quick,
        iq = self.iq,
        mind = self.mind,
        damageReduce = self.damageReduce,
        damageAdd = self.damageAdd,
        level = self.level,
        blood = self.blood,
        blue = self.blue,
        exp = self.exp,
        maxExp = self.maxExp,
        maxFightNum = self.maxFightNum,
        liveState = self.LiveState,
        attackNum = self.attackNum,
        NowSkin = self.NowSkin,
      };
      InFightComponent inFightComponent = self.GetComponent<InFightComponent>();
      if (inFightComponent != null && inFightComponent.InstanceId != 0)
      {
        attackDaoInfo.attackId = inFightComponent.attackId;
        attackDaoInfo.attackTarget = inFightComponent.attackTarget;
        attackDaoInfo.attackState = inFightComponent.attackState;
        attackDaoInfo.fightList = inFightComponent.fightList;
      }
      KilledComponent killedComponent = self.GetComponent<KilledComponent>();
      if (killedComponent != null && killedComponent.InstanceId != 0)
      {
        attackDaoInfo.killed = killedComponent.killed;
        attackDaoInfo.killTime = killedComponent.killTime;
      }
      return attackDaoInfo;
    }

    public static void ParseAttackDaoInfo(this AttackComponent self, AttackDaoInfo attackDaoInfo)
    {
      self.fightInfo = attackDaoInfo.fightInfo;
      self.job = attackDaoInfo.job;
      self.maxBlood = attackDaoInfo.maxBlood;
      self.maxBlue = attackDaoInfo.maxBlue;
      self.minAttack = attackDaoInfo.minAttack;
      self.maxAttack = attackDaoInfo.maxAttack;
      self.realAttack = attackDaoInfo.realAttack;
      self.attackRate = attackDaoInfo.attackRate;
      self.crit = attackDaoInfo.crit;
      self.critDmg = attackDaoInfo.critDmg;
      self.hitRate = attackDaoInfo.hitRate;
      self.miss = attackDaoInfo.miss;
      self.defense = attackDaoInfo.defense;
      self.magicDefense = attackDaoInfo.magicDefense;
      self.strength = attackDaoInfo.strength;
      self.power = attackDaoInfo.power;
      self.quick = attackDaoInfo.quick;
      self.iq = attackDaoInfo.iq;
      self.mind = attackDaoInfo.mind;
      self.damageReduce = attackDaoInfo.damageReduce;
      self.damageAdd = attackDaoInfo.damageAdd;
      self.level = attackDaoInfo.level;
      self.blood = attackDaoInfo.blood;
      self.blue = attackDaoInfo.blue;
      self.exp = attackDaoInfo.exp;
      self.maxExp = attackDaoInfo.maxExp;
      self.maxFightNum = attackDaoInfo.maxFightNum;
      self.LiveState = attackDaoInfo.liveState;
      self.attackNum = attackDaoInfo.attackNum;
      self.NowSkin = attackDaoInfo.NowSkin;
      if (attackDaoInfo.attackTarget != null)
      {
        InFightComponent inFightComponent = self.GetOrAddComponent<InFightComponent>();
        inFightComponent.attackId = attackDaoInfo.attackId;
        inFightComponent.attackTarget = attackDaoInfo.attackTarget;
        inFightComponent.attackState = attackDaoInfo.attackState;
        inFightComponent.fightList = attackDaoInfo.fightList;
      }
      if (attackDaoInfo.killed != null)
      {
        self.RemoveComponent<KilledComponent>();
        KilledComponent killedComponent = self.AddComponent<KilledComponent>();
        killedComponent.killed = attackDaoInfo.killed;
        killedComponent.killTime = attackDaoInfo.killTime;
      }
      if (attackDaoInfo.skillCool != null)
      {
        SkillComponent skillComponent = self.GetOrAddComponent<SkillComponent>();
        skillComponent.skillCool = new ConcurrentDictionary<SkillIdEnum, long>(attackDaoInfo.skillCool);
        skillComponent.skillIds = new List<SkillIdEnum>(attackDaoInfo.skillIds);
        skillComponent.skillMap = new ConcurrentDictionary<SkillIdEnum, Skill>(attackDaoInfo.skillMap);
        skillComponent.equipSkills = attackDaoInfo.equipSkills;
        skillComponent.quickFoods = attackDaoInfo.quickFoods;
      }
      if (attackDaoInfo.equipList != null)
      {
        User user = self.GetParent<User>();
        if (user != null)
        {
          EquipComponent equipComponent = user.GetOrAddComponent<EquipComponent>();
          equipComponent.equipList = attackDaoInfo.equipList;
          equipComponent.specialEquipList = attackDaoInfo.specialEquipList;
        }
      }
    }
  }
}