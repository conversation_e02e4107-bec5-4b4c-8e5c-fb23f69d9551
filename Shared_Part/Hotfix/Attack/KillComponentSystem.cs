namespace MaoYouJi
{
  [EntitySystemOf(typeof(KilledComponent))]
  [FriendOf(typeof(KilledComponent))]
  public static partial class KilledComponentSystem
  {
    [EntitySystem]
    private static void Awake(this KilledComponent self)
    {
    }

    [EntitySystem]
    private static void Destroy(this KilledComponent self)
    {
      self.GetParent<AttackComponent>().LiveState = LiveStateEnum.ALIVE;
    }
  }
}