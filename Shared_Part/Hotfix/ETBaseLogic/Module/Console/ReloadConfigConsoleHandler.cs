using System;

namespace MaoYouJi
{
  [ConsoleHandler(ConsoleMode.ReloadConfig)]
  public class ReloadConfigConsoleHandler : IConsoleHandler
  {
    public async ETTask Run(Fiber fiber, ModeContex contex, string content)
    {
      switch (content)
      {
        case ConsoleMode.ReloadConfig:
          contex.Parent.RemoveComponent<ModeContex>();
          ETLog.Console("C must have config name, like: C UnitConfig");
          break;
        default:
          string[] ss = content.Split(" ");
          string configName = ss[1];
          string category = $"{configName}Category";
          Type type = CodeTypes.Instance.GetType($"MaoYouJi.{category}");
          if (type == null)
          {
            ETLog.Console($"reload config but not find {category}");
            return;
          }
          await ConfigLoader.Instance.Reload(type);
          ETLog.Console($"reload config {configName} finish!");
          break;
      }

      await ETTask.CompletedTask;
    }
  }
}