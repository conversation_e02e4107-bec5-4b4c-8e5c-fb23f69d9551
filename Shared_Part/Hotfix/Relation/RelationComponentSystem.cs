namespace MaoYouJi
{
  [EntitySystemOf(typeof(RelationComponent))]
  [FriendOf(typeof(RelationComponent))]
  public static partial class RelationComponentSystem
  {
    [EntitySystem]
    public static void Awake(this RelationComponent self)
    {
    }

    // 获取关系信息
    public static RelationInfo GetRelationInfo(this RelationComponent self, RelationTypeEnum relationType)
    {
      self.relationInfoMap.TryGetValue(relationType, out RelationInfo relationInfo);
      if (relationInfo == null)
      {
        relationInfo = new RelationInfo
        {
          relationType = relationType,
          level = 0,
          relationExp = 0
        };
        relationInfo.relationMaxExp = getRelationExp(relationInfo.level);
        self.relationInfoMap[relationType] = relationInfo; // 添加到字典中
      }
      return relationInfo;
    }

    public static int getRelationExp(int level)
    {
      if (level < -3 || level > 5)
      {
        ETLog.Error("关系等级错误：" + level);
        return 0;
      }
      return RelationInfo.relationExpList[level + 3];
    }

    public static string getRelationLevelTxt(int level)
    {
      if (level < -3 || level > 5)
      {
        ETLog.Error("关系等级错误：" + level);
        return "普通";
      }
      return RelationInfo.relationLevelTxtList[level + 3];
    }
  }
}