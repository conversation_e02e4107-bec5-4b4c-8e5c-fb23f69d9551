using System.Text;

namespace MaoYouJi
{
  public static class BagProcSys
  {
    public static string GetCoinNumStr(long num)
    {
      StringBuilder sb = new StringBuilder();
      if (num >= 10000)
      {
        sb.Append(num / 10000 + "金");
        num %= 10000;
      }
      if (num >= 100)
      {
        sb.Append(num / 100 + "银");
        num %= 100;
      }
      if (num > 0)
      {
        sb.Append(num + "铜");
      }
      return sb.ToString();
    }
  }
}