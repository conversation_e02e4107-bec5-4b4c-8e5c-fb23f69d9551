using System.Linq;
using ConcurrentCollections;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(TeamInfo))]
  [FriendOf(typeof(TeamInfo))]
  public static partial class TeamInfoSystem
  {
    [EntitySystem]
    private static void Awake(this TeamInfo self)
    {
    }

    public static TeamDaoInfo ToDaoInfo(this TeamInfo self)
    {
      return new TeamDaoInfo
      {
        teamId = self.Id,
        name = self.name,
        description = self.description,
        filterInfo = self.filterInfo,
        isOpen = self.isOpen,
        leaderId = self.leaderId,
        memberInfos = self.memberInfos.ToDictionary(x => x.Key, x => x.Value),
        teamType = self.teamType,
      };
    }

    public static void ParseTeamDaoInfo(this TeamInfo self, TeamDaoInfo teamDaoInfo)
    {
      self.Id = teamDaoInfo.teamId;
      self.name = teamDaoInfo.name;
      self.description = teamDaoInfo.description;
      self.filterInfo = teamDaoInfo.filterInfo;
      self.isOpen = teamDaoInfo.isOpen;
      self.leaderId = teamDaoInfo.leaderId;
      self.teamType = teamDaoInfo.teamType;
      self.memberInfos = new(teamDaoInfo.memberInfos);
    }
  }
}