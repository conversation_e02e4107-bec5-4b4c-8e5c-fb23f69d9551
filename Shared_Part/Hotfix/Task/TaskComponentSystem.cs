using System.Linq;
using ConcurrentCollections;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(TaskComponent))]
  [FriendOf(typeof(TaskComponent))]
  public static partial class TaskComponentSystem
  {
    [EntitySystem]
    private static void Awake(this TaskComponent self)
    {
    }

    public static TaskListDaoInfo GetTaskListDaoInfo(this TaskComponent self)
    {
      TaskListDaoInfo taskListDaoInfo = new()
      {
        finishedTasks = self.finishedTasks.ToList(),
        nowTasks = self.nowTasks
      };
      return taskListDaoInfo;
    }

    public static void ParseTaskListDaoInfo(this TaskComponent self, TaskListDaoInfo taskListDaoInfo)
    {
      self.finishedTasks = new ConcurrentHashSet<TaskIdEnum>(taskListDaoInfo.finishedTasks);
      self.nowTasks = taskListDaoInfo.nowTasks;
    }
  }
}