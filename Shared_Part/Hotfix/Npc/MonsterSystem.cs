namespace MaoYouJi
{
  [EntitySystemOf(typeof(MonsterInfo))]
  [FriendOf(typeof(MonsterInfo))]
  [FriendOf(typeof(AttackComponent))]
  public static partial class MonsterInfoSystem
  {

    [EntitySystem]
    public static void Awake(this MonsterInfo self)
    {
    }

    public static SimpleMon GetSimpleMon(this MonsterInfo self)
    {
      AttackComponent attackComponent = self.GetComponent<AttackComponent>();
      return new SimpleMon()
      {
        id = self.Id,
        monBaseType = self.monBaseType,
        monDescType = self.monDescType,
        monName = self.monName,
        liveState = attackComponent.LiveState
      };
    }

    public static MonDaoInfo GetMonDaoInfo(this MonsterInfo self)
    {
      AttackComponent attackComponent = self.GetComponent<AttackComponent>();
      return new MonDaoInfo()
      {
        monId = self.Id,
        monBaseType = self.monBaseType,
        monDescType = self.monDescType,
        monName = self.monName,
        ownUser = self.ownUser,
        ownName = self.ownName,
        attackDaoInfo = attackComponent.GetSimpleAttackDaoInfo()
      };
    }

    public static void ParseMonDaoInfo(this MonsterInfo self, MonDaoInfo monDaoInfo)
    {
      self.Id = monDaoInfo.monId;
      self.monBaseType = monDaoInfo.monBaseType;
      self.monDescType = monDaoInfo.monDescType;
      self.monName = monDaoInfo.monName;
      self.ownUser = monDaoInfo.ownUser;
      self.ownName = monDaoInfo.ownName;
      AttackComponent attackComponent = self.GetComponent<AttackComponent>();
      attackComponent ??= self.AddComponent<AttackComponent, FightInfo>(monDaoInfo.attackDaoInfo.fightInfo);
      attackComponent.ParseAttackDaoInfo(monDaoInfo.attackDaoInfo);
    }
  }

}
