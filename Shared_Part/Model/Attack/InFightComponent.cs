using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using MongoDB.Bson.Serialization.Attributes;

namespace MaoYouJi
{
  [ComponentOf(typeof(AttackComponent))]
  public partial class InFightComponent : Entity, IAwake
  {
    public long attackId { get; set; } // 战斗的ID，一场战斗的唯一标识
    public FightInfo attackTarget { get; set; } // 攻击目标
    public ConcurrentDictionary<AttackState, AttachStatus> attackState { get; set; } = new(); // 战斗状态列表
    public ConcurrentDictionary<long, FightInfo> fightList { get; set; } // 对战列表，key为战斗对象的ID
  }
}