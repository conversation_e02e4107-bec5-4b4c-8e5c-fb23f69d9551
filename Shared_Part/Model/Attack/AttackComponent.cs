using System.Collections.Generic;

namespace MaoYouJi
{
  [ComponentOf]
  public partial class AttackComponent : Entity, IAwake<FightInfo>, ISerializeToEntity
  {
    public FightInfo fightInfo { get; set; } // 战斗信息
    public BaseJob job { get; set; } // 职业
    public long maxBlood { get; set; } // 最大血量
    public long maxBlue { get; set; } // 最大蓝量
    public long minAttack { get; set; } // 最小攻击力
    public long maxAttack { get; set; } // 最大攻击力
    public long realAttack { get; set; } // 真实攻击力
    public long attackRate { get; set; } // 攻速
    public long skillSpeed { get; set; } = 1000; // 施法速度
    public long crit { get; set; } // 暴击率
    public long critDmg { get; set; } // 暴击伤害
    public long hitRate { get; set; } // 命中率
    public long miss { get; set; } // 闪避率
    public long defense { get; set; } // 物理防御
    public long magicDefense { get; set; } // 魔法防御
    public long strength { get; set; } // 体力
    public long power { get; set; } // 力量
    public long quick { get; set; } // 敏捷
    public long iq { get; set; } // 智力
    public long mind { get; set; } // 精神
    public long damageReduce { get; set; } // 伤害减免比例
    public long damageAdd { get; set; } // 伤害增加比例
    public long level { get; set; } // 等级
    public long blood { get; set; } // 当前血量
    public long blue { get; set; } // 蓝量
    public long exp { get; set; } // 当前经验
    public long maxExp { get; set; } // 当前最大经验
    public long maxFightNum { get; set; } // 最大战斗数量
    private volatile LiveStateEnum liveState;
    public LiveStateEnum LiveState
    {
      get => liveState;
      set => liveState = value;
    } // 战斗状态
    public long attackNum { get; set; } // 战斗力
    public SkinIdEnum NowSkin { get; set; }
  }
}