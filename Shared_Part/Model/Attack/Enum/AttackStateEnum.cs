namespace MaoYouJi
{
  public enum AttackState
  {
    None, // 无
    // 以下为负面属性
    [EnumDescription("夺械")]
    TAKE_WEAPON, // "夺械", "夺械中无法普通攻击"),
    [EnumDescription("沉默")]
    SILENT, // "沉默", "沉默中无法释放技能"),
    [EnumDescription("昏迷")]
    DIZZY, // "昏迷", "昏迷中无法进行任何操作"),
    [EnumDescription("中毒")]
    POISON, // "中毒", "中毒状态下每秒损失一定血量"),
    [EnumDescription("流血")]
    Loss_Blood, // "流血", "流血状态下每秒损失一定血量"),
    [EnumDescription("破甲")]
    ARMOR_BREAK, // "破甲", "破甲状态下受到的护甲降低"),
    [EnumDescription("断刃")]
    BLADE_BREAK, // "断刃", "断刃状态下造成的伤害减少"),
    [EnumDescription("划伤")]
    Hu<PERSON>_<PERSON><PERSON>, // "划伤", "划伤状态下每秒损失一定血量"),
    [EnumDescription("撕裂")]
    Si_Lie, // "撕裂", "撕裂状态下每秒损失一定血量"),
    [EnumDescription("出血")]
    Chu_Xue, // "出血", "出血状态下受到的伤害增加"),
    [EnumDescription("审判")]
    Shen_Pan, // "审判", "审判状态下受到圣魔法的伤害增加", true, false, true, false),
    [EnumDescription("吹飞")]
    Chui_Fei, // "吹飞", "吹飞状态下无法进行攻击，同时无法收到普通伤害"),
    [EnumDescription("狂风笼罩")]
    KuangFeng_LongZhao, // "狂风笼罩", "狂风笼罩状态下攻速降低"),
    [EnumDescription("暗影")]
    AnYin_Jian, // "暗影", "暗影状态下每秒受到伤害"),
    [EnumDescription("震击")]
    LingHun_ZhengJi, // "震击", "震击状态下每秒受到伤害"),
    [EnumDescription("缠绕")]
    ChanRao, // "缠绕", "缠绕状态下无法进行行动"),
    [EnumDescription("迟缓")]
    Chi_Huan, // "迟缓", "迟缓状态下攻速降低"),
    [EnumDescription("冰霜新星")]
    BinShuang_XinXing, // "冰霜新星", "攻击速度降低"),
    [EnumDescription("逃跑")]
    Tao_Pao, // "逃跑", "逃跑状态下无法进行任何操作"),
    [EnumDescription("熔甲")]
    Rong_Jia, // "熔甲", "溶甲状态下防御力降低"),

    // 以下为正面属性
    [EnumDescription("治疗")]
    Cure, // "治疗", "治疗状态下每秒恢复一定血量"),
    [EnumDescription("心灵尖啸")]
    XinLing_JianXiao, // "心灵尖啸", "心灵尖啸状态下普攻伤害翻倍"),
    [EnumDescription("致命一击")]
    ZhiMin_YiJi, // "致命一击", "强化下一次普通攻击"),
    [EnumDescription("疾风")]
    Ji_Feng, // "疾风", "疾风状态下攻速增加"),
    [EnumDescription("魔法奥义")]
    MoFa_AoYi, // "魔法奥义", "强化下一次技能攻击"),
    [EnumDescription("横扫")]
    Heng_Sao, // "横扫", "下一个舍命刺穿变为瞬发技能"),
    [EnumDescription("魔法盾")]
    MoFa_Dun, // "魔法盾", "用法力值来抵挡一定的伤害", true, false, true, false),
    [EnumDescription("灼烧")]
    LieYan_ZhuoShao, // "灼烧", "增强火系法术的伤害", true, false, true, true),
    [EnumDescription("屏障")]
    HuoYan_PinZhang, // "屏障", "形成火焰屏障保护自己，使自己免受非真实伤害。", true, false, true, false),
    [EnumDescription("血狂暴")]
    Xue_KuangBao, // "血狂暴", "增加受到的伤害和造成的伤害"),
    [EnumDescription("圣盾")]
    Shen_Dun, // "圣盾", "增加自己的防御力"),
    [EnumDescription("空手入白刃(武)")]
    KongShouRu_BaiRen, // "空手入白刃(武)", "空手入白刃状态，格挡普通攻击"),
    [EnumDescription("空手入白刃(技)")]
    KongShouRu_BaiRen2, // "空手入白刃(技)", "空手入白刃状态，格挡技能攻击"),
    [EnumDescription("风之力")]
    Feng_ZhiLi, // "风之力", "风系法术伤害提升"),
    [EnumDescription("寒冬")]
    ShenDong_ZhiHan, // "寒冬", "冰系法术暴击率提升", true, false, true, false),
    [EnumDescription("冰冻")]
    FROZEN, // "冰冻", "冰冻中无法进行任何操作，免疫一切伤害"),
    [EnumDescription("法术强化")]
    ADD_MAGIC, // "法术强化", "下一次技能伤害提高"),
    [EnumDescription("强壮")]
    STRONG, // "强壮", "强壮状态下造成的伤害增加"),
    [EnumDescription("坚韧")]
    TOUGH, // "坚韧", "坚韧状态下受到的伤害减少"),
    [EnumDescription("普攻强化")]
    ADD_NORMAL, // "普攻强化", "强化下一次普通攻击"),
    [EnumDescription("免疫")]
    MianYi, // "免疫", "免疫部分负面状态"),
    [EnumDescription("高级免疫")]
    GaoJi_MianYi, // "高级免疫", "免疫所有负面状态"),
    [EnumDescription("铜墙铁壁")]
    TongQiang_TieBi, // "铜墙铁壁", "最终伤害减免100%，且无法受到真实伤害"),
    [EnumDescription("结晶")]
    Jie_Jing, // "结晶", "免疫下一次技能攻击"),
    [EnumDescription("突进")]
    Tu_Jin, // "突进", "攻击速度增加，防御力降低"),
    [EnumDescription("燃烧之心")]
    RanShaoZhi_Xin, // "燃烧之心", "暴击率增加"),
    [EnumDescription("大鹏展翅")]
    DaPeng_ZhanChi, // "大鹏展翅", "吸收所有伤害"),
    [EnumDescription("海妖之歌")]
    HaiYao_ZhiGe, // "海妖之歌", "吸收伤害，返回给对手"),
    [EnumDescription("黑暗之咒")]
    HeiAnZhi_Zhou, // "黑暗之咒", "攻击速度和施法速度提高，受到伤害提高"),
    [EnumDescription("血腥")]
    Xue_Xing, // "血腥", "造成的物理伤害提升"),
    [EnumDescription("防护结界")]
    FangHu_JieJie, // "防护结界", "减免所有伤害"),
    [EnumDescription("暴怒")]
    Bao_Nu, // "暴怒", "造成的伤害提升，闪避率降低，同时无法逃脱"),
    [EnumDescription("钢体")]
    Gang_Ti, // "钢体", "防御力提高"),
    [EnumDescription("敏捷")]
    Min_Jie, // "敏捷", "闪避率增加"),

    // 以下为大逃杀战斗属性
    [EnumDescription("好人卡")]
    HaoRen_Ka, // "好人卡", "攻击速度降低"),
    [EnumDescription("骷髅哥的爱慕")]
    KuLouGe_AiMu, // "骷髅哥的爱慕", "防御率降低"),
    [EnumDescription("小月月的内衣")]
    YueYue_NeiYi, // "小月月的内衣", "攻击力降低"),
    [EnumDescription("女神庇佑")]
    NvShen_PiYou, // "女神庇佑", "防御率增加"),
    [EnumDescription("萝莉控的怨念")]
    LuoLi_YuanNian, // "萝莉控的怨念", "各种属性均增加"),
  }
}