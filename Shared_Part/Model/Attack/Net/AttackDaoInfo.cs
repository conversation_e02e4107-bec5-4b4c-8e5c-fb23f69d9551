using System.Collections.Concurrent;
using System.Collections.Generic;
using MemoryPack;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson.Serialization.Options;

namespace MaoYouJi
{
  [EnableClass]
  [MemoryPackable]
  public partial class AttackDaoInfo
  {
    public FightInfo fightInfo; // 战斗信息
    public BaseJob job; // 职业
    public long maxBlood; // 最大血量
    public long maxBlue; // 最大蓝量
    public long minAttack; // 最小攻击力
    public long maxAttack; // 最大攻击力
    public long realAttack; // 真实攻击力
    public long attackRate; // 攻速
    public long crit; // 暴击率
    public long critDmg; // 暴击伤害
    public long hitRate; // 命中率
    public long miss; // 闪避率
    public long defense; // 物理防御
    public long magicDefense; // 魔法防御
    public long strength; // 体力
    public long power; // 力量
    public long quick; // 敏捷
    public long iq; // 智力
    public long mind; // 精神
    public long damageReduce; // 伤害减免比例
    public long damageAdd; // 伤害增加比例
    public long level; // 等级
    public long blood; // 当前血量
    public long blue; // 蓝量
    public long exp; // 当前经验
    public long maxExp; // 当前最大经验
    public long maxFightNum; // 最大战斗数量
    public List<SkillIdEnum> skillIds; // 技能id列表
    [BsonDictionaryOptions(DictionaryRepresentation.ArrayOfArrays)]
    public Dictionary<SkillIdEnum, Skill> skillMap; // 技能map
    public SkillIdEnum[] equipSkills = new SkillIdEnum[12]; // 装备的技能
    public long[] quickFoods = new long[10]; // 快捷食物
    [BsonDictionaryOptions(DictionaryRepresentation.ArrayOfArrays)]
    public Dictionary<SkillIdEnum, long> skillCool; // 技能冷却
    public LiveStateEnum liveState; // 战斗状态
    public List<Equipment> equipList = new(); // 装备列表
    public List<Thing> specialEquipList = new(); // 特殊装备列表
    public SkinIdEnum NowSkin;
    public long attackId; // 战斗的ID，一场战斗的唯一标识
    public FightInfo attackTarget; // 攻击目标
    [BsonDictionaryOptions(DictionaryRepresentation.ArrayOfArrays)]
    public ConcurrentDictionary<AttackState, AttachStatus> attackState = new(); // 战斗状态列表
    [BsonDictionaryOptions(DictionaryRepresentation.ArrayOfArrays)]
    public ConcurrentDictionary<long, FightInfo> fightList; // 对战列表，key为战斗对象的ID
    public long killTime; // 击杀时间
    public FightInfo killed; // 被杀害的目标
    public long attackNum; // 战斗力
  }

  [EnableClass]
  [MemoryPackable]
  public partial class AttackAttrInfos
  {
    public long maxBlood; // 最大血量
    public long maxBlue; // 最大蓝量
    public long minAttack; // 最小攻击力
    public long maxAttack; // 最大攻击力
    public long realAttack; // 真实攻击力
    public long attackRate; // 攻速
    public long crit; // 暴击率
    public long critDmg; // 暴击伤害
    public long hitRate; // 命中率
    public long miss; // 闪避率
    public long defense; // 物理防御
    public long magicDefense; // 魔法防御
    public long strength; // 体力
    public long power; // 力量
    public long quick; // 敏捷
    public long iq; // 智力
    public long mind; // 精神
    public long level; // 等级
    public long maxExp; // 当前最大经验
    public long attackNum; // 战斗力
  }

  [EnableClass]
  [MemoryPackable]
  public partial class DmgInfo
  {
    public FightInfo src;
    public FightInfo target;
    public DmgType dmgType;
    public long dmgNum;
    public long realNum;
    public long defendNum;
    public bool isHit;
    public bool killed = false;
    public bool revive = false;
    public bool hasCrit = false;
  }

  [EnableClass]
  [MemoryPackable]
  public partial class UpdateAttackInfo
  {
    public long flag; // 更新类型
    public long srcId; // 源id
    public long bloodSub; // 血量减少
    public long blueSub; // 蓝量减少
    public List<AttachStatus> stateList; // 附加状态
    public List<AttackState> removeStateList; // 移除状态
    public LiveStateEnum liveState; // 存活状态
    public Dictionary<SkillIdEnum, long> skillCool; // 技能冷却
    // 击杀信息
    public FightInfo killed;
    public long killTime;

    public bool needUpdateBlood()
    {
      long typeFlag = 1 << (int)UpdateAttackType.Update_Blood;
      return (flag & typeFlag) == typeFlag;
    }

    public bool needUpdateBlue()
    {
      long typeFlag = 1 << (int)UpdateAttackType.Update_Blue;
      return (flag & typeFlag) == typeFlag;
    }

    public bool needUpdateState()
    {
      long typeFlag = 1 << (int)UpdateAttackType.Update_State;
      return (flag & typeFlag) == typeFlag;
    }

    public bool needUpdateLiveState()
    {
      long typeFlag = 1 << (int)UpdateAttackType.Update_Live_State;
      return (flag & typeFlag) == typeFlag;
    }

    public bool needUpdateSkillCool()
    {
      long typeFlag = 1 << (int)UpdateAttackType.Update_Skill_Cool;
      return (flag & typeFlag) == typeFlag;
    }

    public bool needUpdateKilled()
    {
      long typeFlag = 1 << (int)UpdateAttackType.Update_Kill_Info;
      return (flag & typeFlag) == typeFlag;
    }
  }
}