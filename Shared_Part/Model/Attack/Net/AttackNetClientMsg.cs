using MemoryPack;

namespace <PERSON><PERSON>ou<PERSON>i
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientStartAttackMsg)]
  public partial class ClientStartAttackMsg : MaoYouMessage, ILocationMessage
  {
    public FightInfo fightInfo;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientUseSkillMsg)]
  public partial class ClientUseSkillMsg : Mao<PERSON>ouMessage, ILocationMessage, IAttackMessage
  {
    public FightInfo target;
    public SkillIdEnum skillId;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientEscapeMsg)]
  public partial class ClientEscapeMsg : <PERSON>YouMessage, IAttackMessage
  {
  }
}