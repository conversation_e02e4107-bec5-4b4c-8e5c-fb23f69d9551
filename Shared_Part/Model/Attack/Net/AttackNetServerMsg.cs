using System.Collections.Generic;
using MemoryPack;

namespace MaoYouJi
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerStartAttackMsg)]
  public partial class ServerStartAttackMsg : MaoYouMessage, ILocationMessage
  {
    public long attackId;
    public AttackDaoInfo src;
    public AttackDaoInfo target;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerUpdateAttackInfoMsg)]
  public partial class ServerUpdateAttackInfoMsg : MaoYouMessage, ILocationMessage
  {
    public List<UpdateAttackInfo> updateAttackInfos = new();
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerFightChatMsg)]
  public partial class ServerFightChatMsg : MaoYouMessage, ILocationMessage
  {
    public FightChatType type;
    public FightInfo src;
    public FightInfo target;
    public long attackId; // 战斗ID
    public int attackType = 0; // 0为普通攻击，1为技能，2为状态
    public long dmgNum = 0; // 造成的总伤害
    public long cureHp = 0; // 治疗血量
    public long cureSp = 0; // 治疗蓝量
    public long realNum = 0; // 造成的真实伤害
    public long defendNum = 0; // 格挡伤害
    public DmgType dmgType; // 伤害类型
    public string defendSrc; // 格挡来源
    public string dmgSrc; // 伤害来源
    public long songTime; // 时间戳
    public bool isHit = true; // 是否命中
    public bool isAuto = false; // 是否自动移除
    public bool hasCrit = false; // 是否暴击
    public Dictionary<string, string> addStates = new();
    public Dictionary<string, string> delStates = new();
    public Dictionary<string, string> replaceStates = new();
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerChangeTargetMsg)]
  public partial class ServerChangeTargetMsg : MaoYouMessage, ILocationMessage
  {
    public FightInfo src;
    public AttackDaoInfo targetInfo;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerUseSkillMsg)]
  public partial class ServerUseSkillMsg : MaoYouMessage, ILocationMessage
  {
    public FightInfo src;
    public SkillIdEnum skillId;
  }
}