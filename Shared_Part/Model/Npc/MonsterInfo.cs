using System;
using System.Collections.Generic;
using MemoryPack;

namespace MaoYouJi
{
  [EnableClass]
  [MemoryPackable]
  public partial class SimpleMon
  {
    public long id;
    public MonBaseType monBaseType; // 怪物列表
    public MonDescType monDescType; // 怪物类型
    public string monName;
    public LiveStateEnum liveState;
    public long time;
  }

  [EnableClass]
  public class MonSkillInfo : ICloneable
  {
    public SkillIdEnum skillId;
    public int level;
    public long firstUseTime; // 第一次使用的时间，单位为秒，如果为0代表取技能cd

    public object Clone()
    {
      return (MonSkillInfo)MemberwiseClone();
    }
  }

  [ChildOf(typeof(MapNode))]
  public partial class MonsterInfo : Entity, IAwake
  {
    public MonBaseType monBaseType { get; set; } // 怪物列表
    public MonDescType monDescType { get; set; } // 怪物类型
    public string monName { get; set; }
    public long ownUser { get; set; } // 只有特定的人能攻击
    public string ownName { get; set; } // 拥有者姓名
    public long[] attackLevel { get; set; } // 攻击等级，最低到最高
    public List<AttachStatus> selfStatus { get; set; } // 怪物状态，进入战斗时施加给怪物
    public HashSet<MonTagEnum> tags { get; set; } // 怪物标签
    public long genTime { get; set; } // 生成时间
    public long updateTime { get; set; } = 0;
    public List<MonSkillInfo> skillInfos { get; set; } // 技能列表
  }
}