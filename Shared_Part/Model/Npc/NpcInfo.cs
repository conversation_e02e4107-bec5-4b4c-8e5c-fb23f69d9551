using MemoryPack;

namespace Mao<PERSON>ouJi
{
  [EnableClass]
  [MemoryPackable]
  public partial class SimpleNpc
  {
    public long id;
    public NpcNameEnum name;
    public long time;
  }

  [EnableClass]
  [MemoryPackable]
  public partial class LearnSkillInfo
  {
    public SkillIdEnum skillId;
    public int[] canLearnLevels;
  }

  [ChildOf]
  public partial class NpcInfo : Entity, IAwake
  {
    public NpcNameEnum name { get; set; } // NPC的名称
    public string showName { get; set; } // NPC的显示名称
  }
}