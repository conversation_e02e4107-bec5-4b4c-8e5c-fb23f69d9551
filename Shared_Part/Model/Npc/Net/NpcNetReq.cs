using MemoryPack;

namespace MaoYouJi
{
  // NPC消息请求
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ShowMonReq)]
  [ResponseType(nameof(ShowMonResp))]
  public partial class ShowMonReq : MaoYouInMessage, ILocationRequest
  {
    public long MonId;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerNpcTalkListReq)]
  [ResponseType(nameof(ServerNpcTalkListResp))]
  public partial class ServerNpcTalkListReq : MaoYouInMessage, ILocationRequest
  {
    public NpcNameEnum NpcName;
  }
}