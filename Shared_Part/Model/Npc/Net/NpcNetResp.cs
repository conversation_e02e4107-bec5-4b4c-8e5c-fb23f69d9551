using System.Collections.Generic;
using MemoryPack;

namespace MaoYouJi
{
  // NPC消息响应
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ShowMonResp)]
  public partial class ShowMonResp : MaoYouOutMessage, ILocationResponse
  {
    public MonDaoInfo monDaoInfo;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerNpcTalkListResp)]
  public partial class ServerNpcTalkListResp : MaoYouOutMessage, ILocationResponse
  {
    public string talkList;
    public Dictionary<string, string> talkParamsMap = new();
  }
}