using System.Collections.Generic;
using MemoryPack;

namespace <PERSON><PERSON>ou<PERSON>i
{
  // 道士信息
  [MemoryPackable]
  [EnableClass]
  public partial class MonDaoInfo
  {
    public long monId { get; set; } // 怪物id
    public MonBaseType monBaseType { get; set; } // 怪物列表
    public MonDescType monDescType { get; set; } // 怪物类型
    public string monName { get; set; }
    public long ownUser { get; set; } // 只有特定的人能攻击
    public string ownName { get; set; } // 拥有者姓名
    public AttackDaoInfo attackDaoInfo { get; set; }
  }
}