using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Reflection;

namespace MaoYouJi
{
  [AttributeUsage(AttributeTargets.Field)]
  [EnableClass]
  public class EnumDescriptionAttribute : Attribute
  {
    public string Description { get; }

    public EnumDescriptionAttribute(string description)
    {
      Description = description;
    }
  }
  public static class EnumDescriptionCache
  {
    // 使用嵌套字典缓存所有枚举类型的所有值的描述
    [StaticField]
    private static readonly ConcurrentDictionary<Type, Dictionary<Enum, string>> _cache =
      new();

    // 在应用启动时调用此方法预加载指定枚举类型的所有描述
    public static void Initialize<T>() where T : Enum
    {
      Type enumType = typeof(T);

      if (_cache.ContainsKey(enumType))
        return;

      Dictionary<Enum, string> descriptions = new Dictionary<Enum, string>();

      foreach (Enum value in Enum.GetValues(enumType))
      {
        FieldInfo field = enumType.GetField(value.ToString());

        if (field != null)
        {
          EnumDescriptionAttribute attribute = Attribute.GetCustomAttribute(
            field, typeof(EnumDescriptionAttribute)) as EnumDescriptionAttribute;

          descriptions[value] = attribute?.Description ?? value.ToString();
        }
      }

      _cache[enumType] = descriptions;
    }

    // 获取枚举描述的高效方法
    public static string GetDescription<T>(T value) where T : Enum
    {
      Type enumType = typeof(T);

      // 如果缓存中没有这个枚举类型，先初始化
      if (!_cache.TryGetValue(enumType, out Dictionary<Enum, string> descriptions))
      {
        Initialize<T>();
        descriptions = _cache[enumType];
      }

      return descriptions.TryGetValue(value as Enum, out string description)
        ? description
        : value.ToString();
    }
  }
}