using System.Collections.Concurrent;
using System.Collections.Generic;
using ConcurrentCollections;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace MaoYouJi
{
  [EnableClass]
  public class PointInfo
  {
    public string mapName;
    public string pointName;
  }

  [EnableClass]
  public class NearPoint
  {
    public string pointName;
    public int stepNum = 1;
  }

  [EnableClass]
  public class MonsterGen
  {
    public MonBaseType monBaseName;
    public int freshWeight;
  }

  [ChildOf]
  public class MapNode : Entity, IAwake
  {
    public string mapName { get; set; }
    public string pointName { get; set; }
    public int monstorNum { get; set; } = 10;
    public MapNodeType nodeType { get; set; } // 地形类型
    public ConcurrentHashSet<MapNodeState> nodeStates { get; set; } = new(); // 节点状态列表
    public ConcurrentHashSet<NearPoint> nears { get; set; } = new(); // 相邻节点
    public int stepLimit { get; set; } = 10000; // 步数限制，用户在这里最多走几步
    public PointInfo outMap { get; set; }
    [BsonIgnore]
    public ConcurrentDictionary<long, UserInMap> userInPoint { get; set; } = new();
    [BsonIgnore]
    public ConcurrentDictionary<long, SimpleNpc> npcInPoint { get; set; } = new();
    [BsonIgnore]
    public ConcurrentDictionary<long, SimpleMon> monInPoint { get; set; } = new();
  }
}