namespace MaoYouJi
{
  public enum MapNodeType
  {
    CITY, // ("城市"),
    PLAIN, // ("平原"),
    GRASS, // ("草地"),
    FOREST, // ("森林"),
    MOUNTAIN, // ("山地"),
    LAKE, // ("湖泊"),
    RIVER, // ("河流"),
    SNOW, // ("雪地"),
    SAND, // ("沙滩"),
    DESERT, // ("沙漠"),
    MARSH, // ("沼泽"),
    ISLAND, // ("岛屿"),
    CAVE, // ("洞穴");
  }

  public enum MapNodeState
  {
    WEAK_STEP_LIMIT, // ("弱步数限制，可以被坐骑和加速状态减免"),
    STRONG_STEP_LIMIT, // ("强步数限制，忽略一切坐骑和加速状态"),
    FIGHT_USER, // ("可以和用户战斗"),
    OUT_MAP, // ("出地图"),
    ForBidden, // ("禁区"),
    No_Add_User, // ("不能添加用户"),
  }
}