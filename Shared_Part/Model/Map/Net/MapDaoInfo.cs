using System.Collections.Concurrent;
using System.Collections.Generic;
using MemoryPack;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson.Serialization.Options;

namespace MaoYouJi
{
  [EnableClass]
  [MemoryPackable]
  public partial class MapDaoInfo
  {
    public string mapName;
    public string pointName;
    public MapNodeType nodeType;
    public List<MapNodeState> nodeStates;
    [BsonDictionaryOptions(DictionaryRepresentation.ArrayOfArrays)]
    public ConcurrentDictionary<long, UserInMap> userInPoint;
    [BsonDictionaryOptions(DictionaryRepresentation.ArrayOfArrays)]
    public ConcurrentDictionary<long, SimpleNpc> npcInPoint;
    [BsonDictionaryOptions(DictionaryRepresentation.ArrayOfArrays)]
    public ConcurrentDictionary<long, SimpleMon> monInPoint;
  }
}
