using System.Collections.Generic;
using MemoryPack;

namespace <PERSON><PERSON>ouJi
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerMoveOutMsg)]
  public partial class ServerMoveOutMsg : MaoYouMessage, ILocationMessage
  {
    public string targetMap;
    public string targetPoint;
    public MapDaoInfo mapDaoInfo;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerUpdateMapNodeMsg)]
  public partial class ServerUpdateMapNodeMsg : MaoYouMessage, ILocationMessage
  {
    public string mapName;
    public string pointName;
    public List<MapNodeState> updatedStates;
    public List<long> removedIds;
    public List<SimpleMon> updatedMons;
    public List<UserInMap> updatedUsers;
    public List<SimpleNpc> updatedNpcs;
  }
}