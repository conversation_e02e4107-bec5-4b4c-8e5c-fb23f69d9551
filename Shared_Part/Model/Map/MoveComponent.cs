namespace MaoYouJi
{
  [ComponentOf(typeof(MoveComponent))]
  public class AutoMoveComponent : Entity, IAwake<long, string, string>, IDestroy, ISerializeToEntity
  {
    public long time; // 移动的时间间隔，单位为毫秒
    public int maxStep = 1; // 移动的最大格数，小于等于0表示不限制
    public long endTime = 0; // 移动的结束时间，单位为毫秒
    public string targetMap = null; // 目标地图
    public string targetPoint = null; // 目标点
  }

  [ComponentOf]
  public class MoveComponent : Entity, IAwake<string, string, int>, ISerializeToEntity
  {
    public string nowMap { get; set; } = "猫隐村";
    public string nowPoint { get; set; } = "猫隐村广场";
    public int stepLimit { get; set; } = 1;
  }
}