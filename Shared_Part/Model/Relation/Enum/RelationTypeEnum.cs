namespace MaoYouJi
{
  public enum FriendTypeEnum
  {
    None,
    [EnumDescription("好友")]
    Friend,
    [EnumDescription("师徒")]
    Teacher,
    [EnumDescription("婚姻")]
    Lover,
  }

  public enum AddFriendTypeEnum
  {
    None,
    [EnumDescription("交友")]
    AddFriend,
    [EnumDescription("拜师")]
    AddTeacher,
    [EnumDescription("收徒")]
    AddStudent,
    [EnumDescription("求婚")]
    AddLover,
  }

  public enum RelationTypeEnum
  {
    None,
    [EnumDescription("猫隐村关系")]
    MaoYinCun_Relation, // ("猫隐村关系"),
    [EnumDescription("拖把城关系")]
    TuoBaCheng_Relation, // ("拖把城关系"),
    [EnumDescription("雪原城关系")]
    XueYuanCheng_Relation, // ("雪原城关系"),
    [EnumDescription("冰封城关系")]
    BingFengCheng_Relation, // ("冰封城关系"),
    [EnumDescription("白马港关系")]
    BaiMaGang_Relation, // ("白马港关系"),
    [EnumDescription("西赛平原关系")]
    XiSaiPingYuan_Relation, // ("西赛平原关系"),
    [EnumDescription("精灵世界关系")]
    JingLingShiJie_Relation, // ("精灵世界关系"),
    [EnumDescription("盖亚之城关系")]
    GaiYaZhiCheng_Relation, // ("盖亚之城关系"),
    [EnumDescription("希望码头关系")]
    XiWangMaTou_Relation, // ("希望码头关系"),
    [EnumDescription("银月帝国关系")]
    YinYueDiGuo_Relation, // ("银月帝国关系"),
    [EnumDescription("番薯团关系")]
    FanShuTuan_Relation, // ("番薯团关系"),
    [EnumDescription("镖局关系")]
    BiaoJu_Relation, // ("镖局关系"),
    [EnumDescription("官衙关系")]
    GuanYa_Relation, // ("官衙关系"),
    [EnumDescription("猫扑大神关系")]
    MaoPuDaShen_Relation, // ("猫扑大神关系"),
  }
}