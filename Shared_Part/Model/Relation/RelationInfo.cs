using System.Collections.Concurrent;
using System.Collections.Generic;
using MemoryPack;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace MaoYouJi
{
  [ComponentOf(typeof(User))]
  public partial class RelationComponent : <PERSON><PERSON>ty, IAwake, ISerializeToEntity
  {
    public ConcurrentDictionary<RelationTypeEnum, RelationInfo> relationInfoMap { get; set; } = new();
  }

}