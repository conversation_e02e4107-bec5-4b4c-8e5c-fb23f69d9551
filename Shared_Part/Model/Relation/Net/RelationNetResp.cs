using System.Collections.Generic;
using MemoryPack;

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.GetRelationInfoResp)]
  public partial class GetRelationInfoResp : MaoYouOutMessage, ILocationResponse
  {
    public Dictionary<RelationTypeEnum, RelationInfo> RelationInfos = new();
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.GetFriendListResp)]
  public partial class GetFriendListResp : MaoYouOutMessage, ISocialResponse
  {
    public List<FriendInfoRecord> FriendRecords = new();
    public Dictionary<long, ShowSimpleUser> UserSimpleInfos = new();
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ProcAddFriendResp)]
  public partial class ProcAddFriendResp : MaoYouOutMessage, ISocialResponse
  {
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.GetAddFriendListResp)]
  public partial class GetAddFriendListResp : MaoYouOutMessage, ISocialResponse
  {
    public Dictionary<long, ShowSimpleUser> UserSimpleInfos = new();
    public List<AddFriendInfo> AddFriendInfos = new();
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.DelFriendResp)]
  public partial class DelFriendResp : MaoYouOutMessage, ISocialResponse
  {
  }
}