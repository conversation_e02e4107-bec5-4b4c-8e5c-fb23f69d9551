using MemoryPack;
using MongoDB.Bson;

namespace MaoYouJi
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.GetRelationInfoReq)]
  [ResponseType(nameof(GetRelationInfoResp))]
  public partial class GetRelationInfoReq : MaoYouInMessage, ILocationRequest
  {
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.GetFriendListReq)]
  [ResponseType(nameof(GetFriendListResp))]
  public partial class GetFriendListReq : MaoYouInMessage, ISocialRequest
  {
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ProcAddFriendReq)]
  [ResponseType(nameof(ProcAddFriendResp))]
  public partial class ProcAddFriendReq : MaoYouInMessage, ISocialRequest
  {
    public string reqId;
    public int procType; // 0同意，1拒绝
    public int isProcAll; // 0处理单个，1处理所有
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.GetAddFriendListReq)]
  [ResponseType(nameof(GetAddFriendListResp))]
  public partial class GetAddFriendListReq : MaoYouInMessage, ISocialRequest
  {
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.DelFriendReq)]
  [ResponseType(nameof(DelFriendResp))]
  public partial class DelFriendReq : MaoYouInMessage, ISocialRequest
  {
    public long friendRecordId; // 好友记录id
    public FriendTypeEnum friendType; // 好友类型
  }
}