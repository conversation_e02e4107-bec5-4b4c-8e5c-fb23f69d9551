using System.Collections.Generic;
using MemoryPack;

namespace MaoYouJi
{
  [MemoryPackable]
  [EnableClass]
  public partial class RealRode : Thing
  {
    [MemoryPackIgnore]
    public SkinIdEnum skinId; // 皮肤ID
    [MemoryPackIgnore]
    public List<SkillIdEnum> skillIds; // 技能列表
    [MemoryPackIgnore]
    public Dictionary<ThingNameEnum, int> randEggs; // 随机宠物蛋，key为宠物蛋，value为概率

    public override object Clone()
    {
      RealRode realRode = (RealRode)base.Clone();
      realRode.skinId = this.skinId;
      if (this.skillIds != null)
      {
        realRode.skillIds = new List<SkillIdEnum>(this.skillIds);
      }
      if (this.randEggs != null)
      {
        realRode.randEggs = new Dictionary<ThingNameEnum, int>(this.randEggs);
      }
      return realRode;
    }
  }
}