using System.Collections.Concurrent;
using MemoryPack;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson.Serialization.Options;

namespace MaoYouJi
{
  [EnableClass]
  [MemoryPackable]
  public partial class TeamFilterInfo
  {
    public int minLevel; // 最小等级
    public int maxLevel = 100; // 最大等级
    public int minAttack; // 最小战斗力
  }

  [EnableClass]
  [MemoryPackable]
  public partial class TeamMemberInfo
  {
    public long userId; // 用户ID
    public bool isFollow; // 是否跟随队长
    public string name; // 用户名称
    public SkinIdEnum userSkin; // 用户皮肤
    public long attackNum; // 战斗力
    public long blood; // 血量
    public long blue; // 蓝量
    public long maxBlood; // 最大血量
    public long maxBlue; // 最大蓝量
    public int level; // 等级
    public OfflineStateEnum offlineState; // 离线状态
    public LiveStateEnum liveState; // 在线状态
    public string nowMap; // 当前地图
    public string nowPoint; // 当前位置
    public FightInfo attackTarget; // 攻击目标
    public string applyMessage; // 申请信息
  }

  public partial class TeamInfo : Entity, IAwake
  {
    public string name { get; set; } = ""; // 队伍名称
    public string description { get; set; } = ""; // 队伍描述
    public TeamFilterInfo filterInfo { get; set; } = new(); // 队伍筛选条件
    public bool isOpen { get; set; } = false; // 是否公开
    public long leaderId { get; set; } = 0; // 队长ID
    [BsonDictionaryOptions(DictionaryRepresentation.ArrayOfArrays)]
    public ConcurrentDictionary<long, TeamMemberInfo> memberInfos { get; set; } = new(); // 队伍成员信息
    public TeamType teamType { get; set; } = TeamType.Other_Team; // 队伍类型
    public long createTime { get; set; } = 0; // 创建时间
    public long updateTime { get; set; } = 0; // 更新时间
  }
}