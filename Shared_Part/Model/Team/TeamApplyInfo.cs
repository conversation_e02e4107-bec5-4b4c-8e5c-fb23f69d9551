using MemoryPack;

namespace MaoYouJi
{
  [EnableClass]
  [MemoryPackable]
  public partial class TeamApplyInfo
  {
    public long id { get; set; } // 唯一ID
    public long userId { get; set; } // 用户ID
    public long teamId { get; set; } // 队伍ID
    public long applyTime { get; set; } // 申请时间
    public string applyMessage { get; set; } // 申请消息
    public bool isInvite { get; set; } = false; // 是否是邀请
  }
}