using System.Collections.Generic;
using MemoryPack;

namespace <PERSON><PERSON>ouJi
{
  // 队伍服务器消息 48501 - 49000
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerUpdateTeamMsg)]
  public partial class ServerUpdateTeamMsg : MaoYouInMessage, ILocationMessage
  {
    public TeamDaoInfo teamDaoInfo;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerGetApplyListMsg)]
  public partial class ServerGetApplyListMsg : MaoYouInMessage, ILocationMessage
  {
    public List<TeamMemberInfo> applyList; // 申请列表
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerGetInviteListMsg)]
  public partial class ServerGetInviteListMsg : MaoYouInMessage, ILocationMessage
  {
    public List<TeamInviteInfo> inviteInfos;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerProcInviteFollowMsg)]
  public partial class ServerProcInviteFollowMsg : <PERSON>YouInMessage, ILocationMessage
  {
  }
}