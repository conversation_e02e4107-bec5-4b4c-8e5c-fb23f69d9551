using MemoryPack;

namespace <PERSON><PERSON>ouJi
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientCreateTeamMsg)]
  public partial class ClientCreateTeamMsg : MaoYouInMessage, ILocationMessage
  {
    public string teamName; // 队伍名称
    public string teamDesc; // 队伍描述
    public bool isOpen; // 是否公开
    public TeamFilterInfo filterInfo; // 队伍筛选条件
    public TeamType teamType; // 队伍类型
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientGetApplyListMsg)]
  public partial class ClientGetApplyListMsg : MaoYouInMessage, ILocationMessage
  {
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientGetInviteListMsg)]
  public partial class ClientGetInviteListMsg : MaoYouInMessage, ILocationMessage
  {
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientApplyTeamMsg)]
  public partial class ClientApplyTeamMsg : MaoYouInMessage, ILocationMessage
  {
    public long teamId; // 队伍ID
    public string applyMessage; // 申请消息
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientEditTeamMsg)]
  public partial class ClientEditTeamMsg : MaoYouInMessage, ILocationMessage
  {
    public string teamName = null; // 队伍名称
    public string teamDesc = null; // 队伍描述
    public TeamType teamType = TeamType.None; // 队伍类型
    public int? minAttackNum = null; // 最小战斗力
    public int? minLevel = null; // 最小等级
    public int? maxLevel = null; // 最大等级
    public bool? isOpen = null; // 是否公开
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientTransferTeamLeaderMsg)]
  public partial class ClientTransferTeamLeaderMsg : MaoYouInMessage, ILocationMessage
  {
    public long transferUserId; // 新队长ID
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientRemoveTeamMemberMsg)]
  public partial class ClientRemoveTeamMemberMsg : MaoYouInMessage, ILocationMessage
  {
    public long removeUserId; // 移除用户ID
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientProcTeamApplyMsg)]
  public partial class ClientProcTeamApplyMsg : MaoYouInMessage, ILocationMessage
  {
    public long applyUserId;
    public bool isAccept;
    public bool isProcAll = false;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientProcTeamInviteMsg)]
  public partial class ClientProcTeamInviteMsg : MaoYouInMessage, ILocationMessage
  {
    public long teamId; // 队伍ID
    public bool isAccept; // 是否接受
    public bool isProcAll; // 是否处理所有
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientInviteTeamMsg)]
  public partial class ClientInviteTeamMsg : MaoYouInMessage, ILocationMessage
  {
    public long inviteUserId; // 被邀请用户ID
    public string inviteMessage; // 邀请消息
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientInviteFollowMsg)]
  public partial class ClientInviteFollowMsg : MaoYouInMessage, ILocationMessage
  {
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientFollowLeaderMsg)]
  public partial class ClientFollowLeaderMsg : MaoYouInMessage, ILocationMessage
  {
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientQuitTeamMsg)]
  public partial class ClientQuitTeamMsg : MaoYouInMessage, ILocationMessage
  {
  }
}
