using System.Collections.Generic;
using MemoryPack;

namespace <PERSON><PERSON><PERSON>Ji
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.QueryOpenTeamResp)]
  public partial class QueryOpenTeamResp : MaoYouOutMessage, ILocationResponse
  {
    public List<TeamDaoInfo> teamInfos { get; set; } = new();
    public int nowPage { get; set; }
    public int totalPage { get; set; }
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ShowMyTeamResp)]
  public partial class ShowMyTeamResp : MaoYouOutMessage, ILocationResponse
  {
    public TeamDaoInfo teamDaoInfo { get; set; }
  }
}