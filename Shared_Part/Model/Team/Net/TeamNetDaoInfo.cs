using System.Collections.Generic;
using MemoryPack;

namespace MaoYouJi
{
  [MemoryPackable]
  [EnableClass]
  public partial class TeamDaoInfo
  {
    public long teamId { get; set; } = 0; // 队伍ID
    public string name { get; set; } = ""; // 队伍名称
    public string description { get; set; } = ""; // 队伍描述
    public TeamFilterInfo filterInfo { get; set; } = new(); // 队伍筛选条件
    public bool isOpen { get; set; } = false; // 是否公开
    public long leaderId { get; set; } = 0; // 队长ID
    public Dictionary<long, TeamMemberInfo> memberInfos { get; set; } = new(); // 队伍成员信息
    public TeamType teamType { get; set; } = TeamType.Other_Team; // 队伍类型
  }

  [MemoryPackable]
  [EnableClass]
  public partial class TeamInviteInfo
  {
    public TeamDaoInfo teamDaoInfo;
    public string inviteMsg;
  }
}