namespace MaoYouJi
{
  // 队伍消息 48501 - 49000
  public partial class MaoOuterMessageRange
  {
    public const ushort QueryOpenTeamReq = 48501; // 查询开放队伍
    public const ushort QueryOpenTeamResp = 48502; // 查询开放队伍响应
    public const ushort ClientCreateTeamMsg = 48503; // 创建队伍
    public const ushort ServerUpdateTeamMsg = 48504; // 更新队伍
    public const ushort ClientGetApplyListMsg = 48505; // 获取申请列表
    public const ushort ServerGetApplyListMsg = 48506; // 获取申请列表响应
    public const ushort ClientGetInviteListMsg = 48507; // 获取邀请列表
    public const ushort ServerGetInviteListMsg = 48508; // 获取邀请列表响应
    public const ushort ShowMyTeamReq = 48509; // 获取队伍信息
    public const ushort ShowMyTeamResp = 48510; // 获取队伍信息响应
    public const ushort ClientApplyTeamMsg = 48511; // 申请加入队伍
    public const ushort ClientEditTeamMsg = 48512; // 编辑队伍
    public const ushort ClientTransferTeamLeaderMsg = 48513; // 转让队长
    public const ushort ClientRemoveTeamMemberMsg = 48514; // 移除队员
    public const ushort ClientProcTeamApplyMsg = 48515; // 处理队伍申请
    public const ushort ClientProcTeamInviteMsg = 48516; // 处理队伍邀请
    public const ushort ClientInviteTeamMsg = 48517; // 邀请加入队伍
    public const ushort ClientInviteFollowMsg = 48518; // 邀请跟随
    public const ushort ServerProcInviteFollowMsg = 48519; // 处理邀请跟随
    public const ushort ClientFollowLeaderMsg = 48520; // 跟随队长
    public const ushort ClientQuitTeamMsg = 48521; // 退出队伍
  }
}