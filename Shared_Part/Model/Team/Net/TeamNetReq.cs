using MemoryPack;

namespace MaoYouJi
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.QueryOpenTeamReq)]
  [ResponseType(nameof(QueryOpenTeamResp))]
  public partial class QueryOpenTeamReq : MaoYouInMessage, ILocationRequest
  {
    public int page = 0;
    public TeamType teamType = TeamType.None;
    public int minLevel = 0;
    public int maxLevel = 99;
    public int minAttack = 0;
    public bool canEnter = true;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ShowMyTeamReq)]
  [ResponseType(nameof(ShowMyTeamResp))]
  public partial class ShowMyTeamReq : MaoYouInMessage, ILocationRequest
  {
  }
}
