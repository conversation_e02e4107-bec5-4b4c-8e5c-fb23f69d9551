namespace MaoYou<PERSON>i
{
  public enum BaseJob
  {
    None,
    SOLDIER,
    MAGIC
  }

  public enum MaoJob
  {
    None,
    // 战士职业
    [EnumDescription("暗影刺客")]
    AnYin_CiKe, //"暗隐刺客"),
    [EnumDescription("乾坤枪手")]
    <PERSON><PERSON><PERSON><PERSON>_<PERSON>, //"乾坤枪手"),
    [EnumDescription("皇家卫士")]
    <PERSON><PERSON><PERSON>_<PERSON>, //"皇家卫士"),
    [EnumDescription("疾风浪客")]
    Ji<PERSON><PERSON>_LangKe, //"疾风浪客"),
    [EnumDescription("怒灵武者")]
    NuLing_WuZhe, //"怒灵武者"),
    // 法师职业
    [EnumDescription("炎术士")]
    <PERSON>_<PERSON><PERSON>, //"炎术士"),
    [EnumDescription("冰术士")]
    Bin_ShuShi, //"冰术士"),
    [EnumDescription("风术士")]
    Feng_ShuShi, //"风术士"),
    [EnumDescription("天道士")]
    <PERSON><PERSON>_<PERSON>o<PERSON><PERSON>, //"天道士"),
    [EnumDescription("天冥士")]
    <PERSON><PERSON>_<PERSON><PERSON><PERSON>, //"天冥士"),
  }
  public enum UserType
  {
    NORMAL, // 普通用户
    ADMIN, // 管理员
    TEST, // 测试用户
  }

  public enum OfflineStateEnum
  {
    ONLINE,
    NEED_ONLINE,
    OFFLINE
  }

  public enum LiveType
  {
    MONSTER, // ("怪物"),
    ROLE, // ("用户"),
    NPC, // ("NPC");
  }

  public enum LiveStateEnum
  {
    ALIVE, // 正常
    FIGHTING, // 战斗中
    DEAD, //死亡
  }

  public enum UserStateEnum
  {
    ADD_HP, // "回血"
    ADD_SP, // "回蓝"
    ADD_ALL, // "回血回蓝"
    ADD_EXP, // "增加经验"
    Evil_State, // "罪恶状态"
    YingShen_State, // "隐身状态"
    Add_All_Attr, // "所有属性提升"
  }

  public enum UserUpdateFlagEnum
  {
    Blood, // ("血量", 0),
    Blue, // ("蓝量", 1),
    User_State, // ("用户状态", 2),
    Quick_Bar, // ("快捷栏", 3),
    Position, // ("位置", 4),
    Social_Info, // ("社交信息", 5),
    Skill_Info, // ("技能信息", 6),
    Live_State, // ("生命状态", 7),
    Exp, // ("经验", 8),
    Base_Attack, // ("基础属性", 9),
    Equip_List, // ("装备列表", 10),
    NickName, // ("昵称", 11),
  }
}