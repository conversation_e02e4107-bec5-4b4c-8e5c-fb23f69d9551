using System.Collections.Concurrent;
using System.Collections.Generic;
using MemoryPack;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace MaoYouJi
{
  [EnableClass]
  [MemoryPackable]
  public partial class UserTeamInfo
  {
    public long teamId;
    public string teamName;
  }
  [EnableClass]
  [MemoryPackable]
  public partial class UserComInfo
  {
    public long commId; // 公会ID
    public string commName; // 公会名称
  }

  [EnableClass]
  [MemoryPackable]
  public partial class ShowSimpleUser
  {
    public long id;
    public int level;
    public string name;
    public SkinIdEnum nowSkin;
    public OfflineStateEnum offlineState;
    public long lastLogInTime;
  }

  [EnableClass]
  [MemoryPackable]
  public partial class UserInMap
  {
    public long id;
    public string name;
    public OfflineStateEnum offlineState;
    public LiveStateEnum liveState;
    public List<UserStateEnum> userStates;
    public long time;
  }

  [EnableClass]
  [MemoryPackable]
  public partial class FightInfo
  {
    public long fightId;
    public string fightName;
    public MonBaseType monBaseType;
    public LiveType liveType;
  }

  [EnableClass]
  [MemoryPackable]
  public partial class UserStateDetail
  {
    public UserStateEnum userState { get; set; }
    public long existTime { get; set; } = 0;
    public long endTime { get; set; } = 0;
    public long[] val { get; set; }
    // 状态名称
    public string name { get; set; }
    // 状态描述
    public string description { get; set; }
    // 状态描述2
    public string description2 { get; set; }
  }

  [MemoryPackable]
  [ChildOf]
  public partial class User : Entity, IAwake
  {
    public long netAccountId { get; set; } // 账户id
    [MemoryPackIgnore]
    public long accessKey { get; set; } // 访问的key
    public string username { get; set; } // 账户名
    [MemoryPackIgnore]
    public string password { get; set; } // 密码，加密以后存储
    public string nickname { get; set; } // 用户名
    public UserType userType { get; set; }// 用户类型
    public HashSet<SkinIdEnum> skinList { get; set; } = new HashSet<SkinIdEnum>(); // 皮肤列表
    public ActNameEnum activityName { get; set; } = ActNameEnum.None; // 当前活动的名称
    public UserTeamInfo teamInfo { get; set; } // 队伍信息
    public UserComInfo userComInfo { get; set; } // 公会信息
    public ConcurrentDictionary<UserStateEnum, UserStateDetail> userStates { get; set; } = new ConcurrentDictionary<UserStateEnum, UserStateDetail>();
    public OfflineStateEnum offlineState { get; set; } = OfflineStateEnum.ONLINE;
    public int evilNum { get; set; } // 罪恶值
    public long lastLogInTime { get; set; } // 上次登录时间
  }
}