using System.Collections.Concurrent;
using System.Collections.Generic;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace MaoYouJi
{
  [ComponentOf(typeof(User))]
  public class UserVipFuncInfo : Entity, IAwake, ISerializeToEntity
  {
    public long remainLianGongFangTime { get; set; } = 0; // 练功房剩余时间，单位分钟
    public long remainLvGuanTime { get; set; } = 0; // 旅馆剩余时间，单位分钟
    public long remainGaoJiLianGongFangTime { get; set; } = 0; // 高级练功房剩余时间，单位分钟
    public long remainGaoJiLvGuanTime { get; set; } = 0; // 高级旅馆剩余时间，单位分钟
  }

  [ComponentOf(typeof(User))]
  public class UserDailyCntInfo : Entity, IAwake, ISerializeToEntity
  {
    public long lastResetTime { get; set; } = 0; // 上次重置时间
    public int biaoCheCnt { get; set; } = 0; // 押镖次数
    public int shiMingCnt { get; set; } = 0; // 使命次数
    public ConcurrentDictionary<MonBaseType, int> bossKillCnt { get; set; } = new(); // 击杀BOSS次数
  }

  [ComponentOf(typeof(User))]
  public class UserGetInfoCnt : Entity, IAwake, ISerializeToEntity
  {
    public int xinShouLiHeCnt { get; set; } = 0; // 新手礼盒, 1为10级, 2为20级, 3为30级, 4为40级

    public ConcurrentDictionary<ActNameEnum, int> actGetCnt { get; set; } = new(); // 活动领取次数
  }

  // 这个组件的信息全都只存在缓存中，不需要在数据库中存储
  [ComponentOf(typeof(User))]
  public class UserTimeInfo : Entity, IAwake
  {
    [BsonIgnore]
    public long lastCureTime { get; set; } = 0; // 上次治疗时间
    [BsonIgnore]
    public long lastSaveTime { get; set; } = 0; // 上次保存时间
    [BsonIgnore]
    public long lastGlobalChatTime { get; set; } = 0; // 上次全局聊天时间
    [BsonIgnore]
    public long lastChatTime { get; set; } = 0; // 上次聊天时间
    [BsonIgnore]
    public long lastSubEvilNumTime { get; set; } = 0; // 上次减少罪恶值时间
    [BsonIgnore]
    public long lastFreshTime { get; set; } = 0; // 上次刷新时间
    [BsonIgnore]
    public long lastPingTime { get; set; } = 0; // 上次心跳时间
  }
}