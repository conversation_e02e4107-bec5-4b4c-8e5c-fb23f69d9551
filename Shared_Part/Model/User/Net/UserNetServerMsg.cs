using System.Collections.Generic;
using MemoryPack;

// 服务器发送给客户端的消息
namespace MaoYouJi
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerReLogInMsg)]
  public partial class ServerReLogInMsg : MaoYouMessage, IMessage
  {
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerShowUserMsg)]
  public partial class ServerShowUserMsg : MaoYouMessage, IMessage
  {
    public UserBaseDaoInfo userBaseDaoInfo;
    public AttackDaoInfo attackDaoInfo;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerUpdatePartUserInfo)]
  public partial class ServerUpdatePartUserInfoMsg : MaoYouMessage, IMessage
  {
    public long userId;
    public long updateTag; // 更新标签，用于判断哪些信息需要更新
    public long blood;
    public long blue;
    public long exp;
    public string nickname;
    public Dictionary<UserStateEnum, UserStateDetail> userState;
    public long[] quickFoods;
    public SkillIdEnum[] equipSkills;
    public string nowMap;
    public string nowPoint;
    public UserTeamInfo teamInfo;
    public UserComInfo userComInfo;
    public Dictionary<SkillIdEnum, Skill> skillMap;
    public List<SkillIdEnum> skillIds;
    public LiveStateEnum liveState;
    public AttackAttrInfos baseAttack;
    public List<Equipment> equipList;
    public List<Thing> specialEquipList;

    public static ServerUpdatePartUserInfoMsg Create(User user, params UserUpdateFlagEnum[] userUpdateFlagEnums)
    {
      ServerUpdatePartUserInfoMsg msg = new();
      msg.setUpdateInfo(user, userUpdateFlagEnums);
      return msg;
    }

    public void setUpdateInfo(User user, params UserUpdateFlagEnum[] userUpdateFlagEnums)
    {
      this.userId = user.Id;
      // 这几个组件都必须存在的，不存在说明有问题，让系统抛出错误
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      SkillComponent skillComponent = attackComponent.GetComponent<SkillComponent>();
      EquipComponent equipComponent = user.GetComponent<EquipComponent>();
      MoveComponent moveComponent = user.GetComponent<MoveComponent>();
      foreach (UserUpdateFlagEnum userUpdateFlagEnum in userUpdateFlagEnums)
      {
        if (userUpdateFlagEnum == UserUpdateFlagEnum.Blood)
        {
          this.blood = attackComponent.blood;
        }
        else if (userUpdateFlagEnum == UserUpdateFlagEnum.Blue)
        {
          this.blue = attackComponent.blue;
        }
        else if (userUpdateFlagEnum == UserUpdateFlagEnum.User_State)
        {
          this.userState = new Dictionary<UserStateEnum, UserStateDetail>(user.userStates);
        }
        else if (userUpdateFlagEnum == UserUpdateFlagEnum.Quick_Bar)
        {
          this.quickFoods = skillComponent.quickFoods;
          this.equipSkills = skillComponent.equipSkills;
        }
        else if (userUpdateFlagEnum == UserUpdateFlagEnum.Position)
        {
          this.nowMap = moveComponent.nowMap;
          this.nowPoint = moveComponent.nowPoint;
        }
        else if (userUpdateFlagEnum == UserUpdateFlagEnum.Social_Info)
        {
          this.teamInfo = user.teamInfo;
          this.userComInfo = user.userComInfo;
        }
        else if (userUpdateFlagEnum == UserUpdateFlagEnum.Skill_Info)
        {
          // 采用UpdateSimpleSkillInfoOut增量更新
        }
        else if (userUpdateFlagEnum == UserUpdateFlagEnum.Live_State)
        {
          this.liveState = attackComponent.LiveState;
        }
        else if (userUpdateFlagEnum == UserUpdateFlagEnum.Exp)
        {
          this.exp = attackComponent.exp;
        }
        else if (userUpdateFlagEnum == UserUpdateFlagEnum.Base_Attack)
        {
          this.baseAttack = new AttackAttrInfos();
          this.baseAttack.maxBlood = attackComponent.maxBlood;
          this.baseAttack.maxBlue = attackComponent.maxBlue;
          this.baseAttack.minAttack = attackComponent.minAttack;
          this.baseAttack.maxAttack = attackComponent.maxAttack;
          this.baseAttack.realAttack = attackComponent.realAttack;
          this.baseAttack.attackRate = attackComponent.attackRate;
          this.baseAttack.crit = attackComponent.crit;
          this.baseAttack.critDmg = attackComponent.critDmg;
          this.baseAttack.hitRate = attackComponent.hitRate;
          this.baseAttack.miss = attackComponent.miss;
          this.baseAttack.defense = attackComponent.defense;
          this.baseAttack.magicDefense = attackComponent.magicDefense;
          this.baseAttack.strength = attackComponent.strength;
          this.baseAttack.power = attackComponent.power;
          this.baseAttack.quick = attackComponent.quick;
          this.baseAttack.iq = attackComponent.iq;
          this.baseAttack.mind = attackComponent.mind;
          this.baseAttack.level = attackComponent.level;
          this.baseAttack.maxExp = attackComponent.maxExp;
          this.baseAttack.attackNum = attackComponent.attackNum;
        }
        else if (userUpdateFlagEnum == UserUpdateFlagEnum.Equip_List)
        {
          this.equipList = equipComponent.equipList;
          this.specialEquipList = equipComponent.specialEquipList;
        }
        else if (userUpdateFlagEnum == UserUpdateFlagEnum.NickName)
        {
          this.nickname = user.nickname;
        }
      }
      setUpdateTag(userUpdateFlagEnums);
    }

    public void setUpdateTag(params UserUpdateFlagEnum[] userUpdateFlagEnums)
    {
      foreach (UserUpdateFlagEnum userUpdateFlagEnum in userUpdateFlagEnums)
      {
        updateTag |= 1L << (int)userUpdateFlagEnum;
      }
    }
  }
}