using System.Collections.Concurrent;
using System.Collections.Generic;
using MemoryPack;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson.Serialization.Options;

namespace MaoYouJi
{
  [EnableClass]
  [MemoryPackable]
  public partial class ServerData
  {
    public int zone;
    public string name;
    public bool isEnable;
    public int maxOnlineNum;
    public int nowOnlineNum;
  }

  [EnableClass]
  [MemoryPackable]
  public partial class TapTapAccessToken
  {
    [MemoryPackOrder(0)]
    public string kid;
    [MemoryPackOrder(1)]
    public string tokenType;
    [MemoryPackOrder(2)]
    public string macKey;
    [MemoryPackOrder(3)]
    public string macAlgorithm;
    [MemoryPackOrder(4)]
    public string openId;
    [MemoryPackOrder(5)]
    public string unionId;
  }

  [EnableClass]
  [MemoryPackable]
  public partial class AccountRoleInfo
  {
    public long accountId;
    public long userId;
    public string name;
    public SkinIdEnum skinId;
    public long level;
    public BaseJob job;
  }

  [EnableClass]
  [MemoryPackable]
  public partial class UserBaseDaoInfo
  {
    public long id;
    public long netAccountId;
    public string name;
    public UserType userType;
    public UserTeamInfo teamInfo; // 队伍信息
    public UserComInfo userComInfo; // 公会信息
    [BsonDictionaryOptions(DictionaryRepresentation.ArrayOfArrays)]
    public ConcurrentDictionary<UserStateEnum, UserStateDetail> userStates = new ConcurrentDictionary<UserStateEnum, UserStateDetail>();
    public OfflineStateEnum offlineState = OfflineStateEnum.ONLINE;
    public int evilNum; // 罪恶值
    public string nowMap;
    public string nowPoint;
    public int stepLimit;
  }
}