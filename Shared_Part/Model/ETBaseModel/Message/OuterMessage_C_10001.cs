using MemoryPack;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson.Serialization.Options;
using System.Collections.Generic;

namespace MaoYouJi
{
  [MemoryPackable]
  [Message(OuterMessage.HttpGetRouterResponse)]
  public partial class HttpGetRouterResponse : MessageObject
  {
    public static HttpGetRouterResponse Create(bool isFromPool = false)
    {
      return ObjectPool.Instance.Fetch(typeof(HttpGetRouterResponse), isFromPool) as HttpGetRouterResponse;
    }

    [MemoryPackOrder(0)]
    public List<string> Realms { get; set; } = new();

    [MemoryPackOrder(1)]
    public List<string> Routers { get; set; } = new();
    [MemoryPackOrder(2)]
    [BsonDictionaryOptions(DictionaryRepresentation.ArrayOfArrays)]
    public Dictionary<int, List<string>> Gates { get; set; } = new();

    public override void Dispose()
    {
      if (!this.IsFromPool)
      {
        return;
      }

      this.Realms.Clear();
      this.Routers.Clear();

      ObjectPool.Instance.Recycle(this);
    }
  }

  [MemoryPackable]
  [Message(OuterMessage.HttpGetServerVersionResponse)]
  public partial class HttpGetServerVersionResponse : MessageObject
  {
    public static HttpGetServerVersionResponse Create(bool isFromPool = false)
    {
      return ObjectPool.Instance.Fetch(typeof(HttpGetServerVersionResponse), isFromPool) as HttpGetServerVersionResponse;
    }

    [MemoryPackOrder(0)]
    public string ServerVersion { get; set; }

    public override void Dispose()
    {
      if (!this.IsFromPool)
      {
        return;
      }

      this.ServerVersion = default;

      ObjectPool.Instance.Recycle(this);
    }
  }

  [MemoryPackable]
  [Message(OuterMessage.HttpReloadHotfixResponse)]
  public partial class HttpReloadHotfixResponse : MessageObject
  {
    public static HttpReloadHotfixResponse Create(bool isFromPool = false)
    {
      return ObjectPool.Instance.Fetch(typeof(HttpReloadHotfixResponse), isFromPool) as HttpReloadHotfixResponse;
    }

    [MemoryPackOrder(0)]
    public string ResponseMsg { get; set; }

    public override void Dispose()
    {
      if (!this.IsFromPool)
      {
        return;
      }

      this.ResponseMsg = default;

      ObjectPool.Instance.Recycle(this);
    }
  }

  [MemoryPackable]
  [Message(OuterMessage.RouterSync)]
  public partial class RouterSync : MessageObject
  {
    public static RouterSync Create(bool isFromPool = false)
    {
      return ObjectPool.Instance.Fetch(typeof(RouterSync), isFromPool) as RouterSync;
    }

    [MemoryPackOrder(0)]
    public uint ConnectId { get; set; }

    [MemoryPackOrder(1)]
    public string Address { get; set; }

    public override void Dispose()
    {
      if (!this.IsFromPool)
      {
        return;
      }

      this.ConnectId = default;
      this.Address = default;

      ObjectPool.Instance.Recycle(this);
    }
  }

  [MemoryPackable]
  [Message(OuterMessage.C2G_EnterMap)]
  [ResponseType(nameof(G2C_EnterMap))]
  public partial class C2G_EnterMap : MessageObject, ISessionRequest
  {
    public static C2G_EnterMap Create(bool isFromPool = false)
    {
      return ObjectPool.Instance.Fetch(typeof(C2G_EnterMap), isFromPool) as C2G_EnterMap;
    }

    [MemoryPackOrder(0)]
    public int RpcId { get; set; }

    public override void Dispose()
    {
      if (!this.IsFromPool)
      {
        return;
      }

      this.RpcId = default;

      ObjectPool.Instance.Recycle(this);
    }
  }

  [MemoryPackable]
  [Message(OuterMessage.G2C_EnterMap)]
  public partial class G2C_EnterMap : MessageObject, ISessionResponse
  {
    public static G2C_EnterMap Create(bool isFromPool = false)
    {
      return ObjectPool.Instance.Fetch(typeof(G2C_EnterMap), isFromPool) as G2C_EnterMap;
    }

    [MemoryPackOrder(0)]
    public int RpcId { get; set; }

    [MemoryPackOrder(1)]
    public int Error { get; set; }

    [MemoryPackOrder(2)]
    public string Message { get; set; }

    /// <summary>
    /// 自己的UnitId
    /// </summary>
    [MemoryPackOrder(3)]
    public long MyId { get; set; }

    public override void Dispose()
    {
      if (!this.IsFromPool)
      {
        return;
      }

      this.RpcId = default;
      this.Error = default;
      this.Message = default;
      this.MyId = default;

      ObjectPool.Instance.Recycle(this);
    }
  }

  [MemoryPackable]
  [Message(OuterMessage.MoveInfo)]
  public partial class MoveInfo : MessageObject
  {
    public static MoveInfo Create(bool isFromPool = false)
    {
      return ObjectPool.Instance.Fetch(typeof(MoveInfo), isFromPool) as MoveInfo;
    }

    [MemoryPackOrder(0)]
    public List<Unity.Mathematics.float3> Points { get; set; } = new();

    [MemoryPackOrder(1)]
    public Unity.Mathematics.quaternion Rotation { get; set; }

    [MemoryPackOrder(2)]
    public int TurnSpeed { get; set; }

    public override void Dispose()
    {
      if (!this.IsFromPool)
      {
        return;
      }

      this.Points.Clear();
      this.Rotation = default;
      this.TurnSpeed = default;

      ObjectPool.Instance.Recycle(this);
    }
  }

  [MemoryPackable]
  [Message(OuterMessage.UnitInfo)]
  public partial class UnitInfo : MessageObject
  {
    public static UnitInfo Create(bool isFromPool = false)
    {
      return ObjectPool.Instance.Fetch(typeof(UnitInfo), isFromPool) as UnitInfo;
    }

    [MemoryPackOrder(0)]
    public long UnitId { get; set; }

    [MemoryPackOrder(1)]
    public int ConfigId { get; set; }

    [MemoryPackOrder(2)]
    public int Type { get; set; }

    [MemoryPackOrder(3)]
    public Unity.Mathematics.float3 Position { get; set; }

    [MemoryPackOrder(4)]
    public Unity.Mathematics.float3 Forward { get; set; }

    [MongoDB.Bson.Serialization.Attributes.BsonDictionaryOptions(MongoDB.Bson.Serialization.Options.DictionaryRepresentation.ArrayOfArrays)]
    [MemoryPackOrder(5)]
    public Dictionary<int, long> KV { get; set; } = new();
    [MemoryPackOrder(6)]
    public MoveInfo MoveInfo { get; set; }

    public override void Dispose()
    {
      if (!this.IsFromPool)
      {
        return;
      }

      this.UnitId = default;
      this.ConfigId = default;
      this.Type = default;
      this.Position = default;
      this.Forward = default;
      this.KV.Clear();
      this.MoveInfo = default;

      ObjectPool.Instance.Recycle(this);
    }
  }

  [MemoryPackable]
  [Message(OuterMessage.M2C_CreateUnits)]
  public partial class M2C_CreateUnits : MessageObject, IMessage
  {
    public static M2C_CreateUnits Create(bool isFromPool = false)
    {
      return ObjectPool.Instance.Fetch(typeof(M2C_CreateUnits), isFromPool) as M2C_CreateUnits;
    }

    [MemoryPackOrder(0)]
    public List<UnitInfo> Units { get; set; } = new();

    public override void Dispose()
    {
      if (!this.IsFromPool)
      {
        return;
      }

      this.Units.Clear();

      ObjectPool.Instance.Recycle(this);
    }
  }

  [MemoryPackable]
  [Message(OuterMessage.M2C_CreateMyUnit)]
  public partial class M2C_CreateMyUnit : MessageObject, IMessage
  {
    public static M2C_CreateMyUnit Create(bool isFromPool = false)
    {
      return ObjectPool.Instance.Fetch(typeof(M2C_CreateMyUnit), isFromPool) as M2C_CreateMyUnit;
    }

    [MemoryPackOrder(0)]
    public UnitInfo Unit { get; set; }

    public override void Dispose()
    {
      if (!this.IsFromPool)
      {
        return;
      }

      this.Unit = default;

      ObjectPool.Instance.Recycle(this);
    }
  }

  [MemoryPackable]
  [Message(OuterMessage.M2C_StartSceneChange)]
  public partial class M2C_StartSceneChange : MessageObject, IMessage
  {
    public static M2C_StartSceneChange Create(bool isFromPool = false)
    {
      return ObjectPool.Instance.Fetch(typeof(M2C_StartSceneChange), isFromPool) as M2C_StartSceneChange;
    }

    [MemoryPackOrder(0)]
    public long SceneInstanceId { get; set; }

    [MemoryPackOrder(1)]
    public string SceneName { get; set; }

    public override void Dispose()
    {
      if (!this.IsFromPool)
      {
        return;
      }

      this.SceneInstanceId = default;
      this.SceneName = default;

      ObjectPool.Instance.Recycle(this);
    }
  }

  [MemoryPackable]
  [Message(OuterMessage.M2C_RemoveUnits)]
  public partial class M2C_RemoveUnits : MessageObject, IMessage
  {
    public static M2C_RemoveUnits Create(bool isFromPool = false)
    {
      return ObjectPool.Instance.Fetch(typeof(M2C_RemoveUnits), isFromPool) as M2C_RemoveUnits;
    }

    [MemoryPackOrder(0)]
    public List<long> Units { get; set; } = new();

    public override void Dispose()
    {
      if (!this.IsFromPool)
      {
        return;
      }

      this.Units.Clear();

      ObjectPool.Instance.Recycle(this);
    }
  }

  [MemoryPackable]
  [Message(OuterMessage.M2C_PathfindingResult)]
  public partial class M2C_PathfindingResult : MessageObject, IMessage
  {
    public static M2C_PathfindingResult Create(bool isFromPool = false)
    {
      return ObjectPool.Instance.Fetch(typeof(M2C_PathfindingResult), isFromPool) as M2C_PathfindingResult;
    }

    [MemoryPackOrder(0)]
    public long Id { get; set; }

    [MemoryPackOrder(1)]
    public Unity.Mathematics.float3 Position { get; set; }

    [MemoryPackOrder(2)]
    public List<Unity.Mathematics.float3> Points { get; set; } = new();

    public override void Dispose()
    {
      if (!this.IsFromPool)
      {
        return;
      }

      this.Id = default;
      this.Position = default;
      this.Points.Clear();

      ObjectPool.Instance.Recycle(this);
    }
  }

  [MemoryPackable]
  [Message(OuterMessage.M2C_Stop)]
  public partial class M2C_Stop : MessageObject, IMessage
  {
    public static M2C_Stop Create(bool isFromPool = false)
    {
      return ObjectPool.Instance.Fetch(typeof(M2C_Stop), isFromPool) as M2C_Stop;
    }

    [MemoryPackOrder(0)]
    public int Error { get; set; }

    [MemoryPackOrder(1)]
    public long Id { get; set; }

    [MemoryPackOrder(2)]
    public Unity.Mathematics.float3 Position { get; set; }

    [MemoryPackOrder(3)]
    public Unity.Mathematics.quaternion Rotation { get; set; }

    public override void Dispose()
    {
      if (!this.IsFromPool)
      {
        return;
      }

      this.Error = default;
      this.Id = default;
      this.Position = default;
      this.Rotation = default;

      ObjectPool.Instance.Recycle(this);
    }
  }

  [MemoryPackable]
  [Message(OuterMessage.C2G_Ping)]
  [ResponseType(nameof(G2C_Ping))]
  public partial class C2G_Ping : MessageObject, ISessionRequest
  {
    public static C2G_Ping Create(bool isFromPool = false)
    {
      return ObjectPool.Instance.Fetch(typeof(C2G_Ping), isFromPool) as C2G_Ping;
    }

    [MemoryPackOrder(0)]
    public int RpcId { get; set; }

    public override void Dispose()
    {
      if (!this.IsFromPool)
      {
        return;
      }

      this.RpcId = default;

      ObjectPool.Instance.Recycle(this);
    }
  }

  [MemoryPackable]
  [Message(OuterMessage.G2C_Ping)]
  public partial class G2C_Ping : MessageObject, ISessionResponse
  {
    public static G2C_Ping Create(bool isFromPool = false)
    {
      return ObjectPool.Instance.Fetch(typeof(G2C_Ping), isFromPool) as G2C_Ping;
    }

    [MemoryPackOrder(0)]
    public int RpcId { get; set; }

    [MemoryPackOrder(1)]
    public int Error { get; set; }

    [MemoryPackOrder(2)]
    public string Message { get; set; }

    [MemoryPackOrder(3)]
    public long Time { get; set; }

    public override void Dispose()
    {
      if (!this.IsFromPool)
      {
        return;
      }

      this.RpcId = default;
      this.Error = default;
      this.Message = default;
      this.Time = default;

      ObjectPool.Instance.Recycle(this);
    }
  }

  [MemoryPackable]
  [Message(OuterMessage.G2C_Test)]
  public partial class G2C_Test : MessageObject, ISessionMessage
  {
    public static G2C_Test Create(bool isFromPool = false)
    {
      return ObjectPool.Instance.Fetch(typeof(G2C_Test), isFromPool) as G2C_Test;
    }

    public override void Dispose()
    {
      if (!this.IsFromPool)
      {
        return;
      }


      ObjectPool.Instance.Recycle(this);
    }
  }

  [MemoryPackable]
  [Message(OuterMessage.C2M_Reload)]
  [ResponseType(nameof(M2C_Reload))]
  public partial class C2M_Reload : MessageObject, ISessionRequest
  {
    public static C2M_Reload Create(bool isFromPool = false)
    {
      return ObjectPool.Instance.Fetch(typeof(C2M_Reload), isFromPool) as C2M_Reload;
    }

    [MemoryPackOrder(0)]
    public int RpcId { get; set; }

    [MemoryPackOrder(1)]
    public string Account { get; set; }

    [MemoryPackOrder(2)]
    public string Password { get; set; }

    public override void Dispose()
    {
      if (!this.IsFromPool)
      {
        return;
      }

      this.RpcId = default;
      this.Account = default;
      this.Password = default;

      ObjectPool.Instance.Recycle(this);
    }
  }

  [MemoryPackable]
  [Message(OuterMessage.M2C_Reload)]
  public partial class M2C_Reload : MessageObject, ISessionResponse
  {
    public static M2C_Reload Create(bool isFromPool = false)
    {
      return ObjectPool.Instance.Fetch(typeof(M2C_Reload), isFromPool) as M2C_Reload;
    }

    [MemoryPackOrder(0)]
    public int RpcId { get; set; }

    [MemoryPackOrder(1)]
    public int Error { get; set; }

    [MemoryPackOrder(2)]
    public string Message { get; set; }

    public override void Dispose()
    {
      if (!this.IsFromPool)
      {
        return;
      }

      this.RpcId = default;
      this.Error = default;
      this.Message = default;

      ObjectPool.Instance.Recycle(this);
    }
  }

  [MemoryPackable]
  [Message(OuterMessage.C2R_Login)]
  [ResponseType(nameof(R2C_Login))]
  public partial class C2R_Login : MessageObject, ISessionRequest
  {
    public static C2R_Login Create(bool isFromPool = false)
    {
      return ObjectPool.Instance.Fetch(typeof(C2R_Login), isFromPool) as C2R_Login;
    }

    [MemoryPackOrder(0)]
    public int RpcId { get; set; }

    /// <summary>
    /// 帐号
    /// </summary>
    [MemoryPackOrder(1)]
    public TapTapAccessToken tapTapAccessToken { get; set; }

    public override void Dispose()
    {
      if (!this.IsFromPool)
      {
        return;
      }

      this.RpcId = default;
      this.tapTapAccessToken = default;

      ObjectPool.Instance.Recycle(this);
    }
  }

  [MemoryPackable]
  [Message(OuterMessage.R2C_Login)]
  public partial class R2C_Login : MessageObject, ISessionResponse
  {
    public static R2C_Login Create(bool isFromPool = false)
    {
      return ObjectPool.Instance.Fetch(typeof(R2C_Login), isFromPool) as R2C_Login;
    }

    [MemoryPackOrder(0)]
    public int RpcId { get; set; }

    [MemoryPackOrder(1)]
    public int Error { get; set; }

    [MemoryPackOrder(2)]
    public string Message { get; set; }
    [MemoryPackOrder(3)]
    public List<ServerData> serverList;

    public override void Dispose()
    {
      if (!this.IsFromPool)
      {
        return;
      }
      this.RpcId = default;
      this.Error = default;
      this.Message = default;
      this.serverList = default;
      ObjectPool.Instance.Recycle(this);
    }
  }

  [MemoryPackable]
  [Message(OuterMessage.C2G_LoginGate)]
  [ResponseType(nameof(G2C_LoginGate))]
  public partial class C2G_LoginGate : MessageObject, ISessionRequest
  {
    public static C2G_LoginGate Create(bool isFromPool = false)
    {
      return ObjectPool.Instance.Fetch(typeof(C2G_LoginGate), isFromPool) as C2G_LoginGate;
    }

    [MemoryPackOrder(0)]
    public int RpcId { get; set; }

    /// <summary>
    /// 帐号
    /// </summary>
    [MemoryPackOrder(1)]
    public long Key { get; set; }

    [MemoryPackOrder(2)]
    public long GateId { get; set; }

    public override void Dispose()
    {
      if (!this.IsFromPool)
      {
        return;
      }

      this.RpcId = default;
      this.Key = default;
      this.GateId = default;

      ObjectPool.Instance.Recycle(this);
    }
  }

  [MemoryPackable]
  [Message(OuterMessage.G2C_LoginGate)]
  public partial class G2C_LoginGate : MessageObject, ISessionResponse
  {
    public static G2C_LoginGate Create(bool isFromPool = false)
    {
      return ObjectPool.Instance.Fetch(typeof(G2C_LoginGate), isFromPool) as G2C_LoginGate;
    }

    [MemoryPackOrder(0)]
    public int RpcId { get; set; }

    [MemoryPackOrder(1)]
    public int Error { get; set; }

    [MemoryPackOrder(2)]
    public string Message { get; set; }

    [MemoryPackOrder(3)]
    public long PlayerId { get; set; }

    public override void Dispose()
    {
      if (!this.IsFromPool)
      {
        return;
      }

      this.RpcId = default;
      this.Error = default;
      this.Message = default;
      this.PlayerId = default;

      ObjectPool.Instance.Recycle(this);
    }
  }

  [MemoryPackable]
  [Message(OuterMessage.G2C_TestHotfixMessage)]
  public partial class G2C_TestHotfixMessage : MessageObject, ISessionMessage
  {
    public static G2C_TestHotfixMessage Create(bool isFromPool = false)
    {
      return ObjectPool.Instance.Fetch(typeof(G2C_TestHotfixMessage), isFromPool) as G2C_TestHotfixMessage;
    }

    [MemoryPackOrder(0)]
    public string Info { get; set; }

    public override void Dispose()
    {
      if (!this.IsFromPool)
      {
        return;
      }

      this.Info = default;

      ObjectPool.Instance.Recycle(this);
    }
  }

  [MemoryPackable]
  [Message(OuterMessage.C2G_Benchmark)]
  [ResponseType(nameof(G2C_Benchmark))]
  public partial class C2G_Benchmark : MessageObject, ISessionRequest
  {
    public static C2G_Benchmark Create(bool isFromPool = false)
    {
      return ObjectPool.Instance.Fetch(typeof(C2G_Benchmark), isFromPool) as C2G_Benchmark;
    }

    [MemoryPackOrder(0)]
    public int RpcId { get; set; }

    public override void Dispose()
    {
      if (!this.IsFromPool)
      {
        return;
      }

      this.RpcId = default;

      ObjectPool.Instance.Recycle(this);
    }
  }

  [MemoryPackable]
  [Message(OuterMessage.G2C_Benchmark)]
  public partial class G2C_Benchmark : MessageObject, ISessionResponse
  {
    public static G2C_Benchmark Create(bool isFromPool = false)
    {
      return ObjectPool.Instance.Fetch(typeof(G2C_Benchmark), isFromPool) as G2C_Benchmark;
    }

    [MemoryPackOrder(0)]
    public int RpcId { get; set; }

    [MemoryPackOrder(1)]
    public int Error { get; set; }

    [MemoryPackOrder(2)]
    public string Message { get; set; }

    public override void Dispose()
    {
      if (!this.IsFromPool)
      {
        return;
      }

      this.RpcId = default;
      this.Error = default;
      this.Message = default;

      ObjectPool.Instance.Recycle(this);
    }
  }
  [EnableClass]
  public partial class OuterMessage
  {
    public const ushort HttpGetRouterResponse = 10002;
    public const ushort HttpGetServerVersionResponse = 10500;
    public const ushort HttpReloadHotfixResponse = 10501;
    public const ushort RouterSync = 10003;
    public const ushort C2M_TestRequest = 10004;
    public const ushort M2C_TestResponse = 10005;
    public const ushort C2G_EnterMap = 10006;
    public const ushort G2C_EnterMap = 10007;
    public const ushort MoveInfo = 10008;
    public const ushort UnitInfo = 10009;
    public const ushort M2C_CreateUnits = 10010;
    public const ushort M2C_CreateMyUnit = 10011;
    public const ushort M2C_StartSceneChange = 10012;
    public const ushort M2C_RemoveUnits = 10013;
    public const ushort C2M_PathfindingResult = 10014;
    public const ushort C2M_Stop = 10015;
    public const ushort M2C_PathfindingResult = 10016;
    public const ushort M2C_Stop = 10017;
    public const ushort C2G_Ping = 10018;
    public const ushort G2C_Ping = 10019;
    public const ushort G2C_Test = 10020;
    public const ushort C2M_Reload = 10021;
    public const ushort M2C_Reload = 10022;
    public const ushort C2R_Login = 10023;
    public const ushort R2C_Login = 10024;
    public const ushort C2G_LoginGate = 10025;
    public const ushort G2C_LoginGate = 10026;
    public const ushort G2C_TestHotfixMessage = 10027;
    public const ushort C2M_TestRobotCase = 10028;
    public const ushort M2C_TestRobotCase = 10029;
    public const ushort C2M_TestRobotCase2 = 10030;
    public const ushort M2C_TestRobotCase2 = 10031;
    public const ushort C2M_TransferMap = 10032;
    public const ushort M2C_TransferMap = 10033;
    public const ushort C2G_Benchmark = 10034;
    public const ushort G2C_Benchmark = 10035;
  }
}