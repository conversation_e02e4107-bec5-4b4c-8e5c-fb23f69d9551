using MemoryPack;
using System.Collections.Generic;

namespace MaoYouJi
{
  [MemoryPackable]
  [Message(ClientMessage.Main2NetClient_Login)]
  [ResponseType(nameof(NetClient2Main_Login))]
  public partial class Main2NetClient_Login : MessageObject, IRequest
  {
    public static Main2NetClient_Login Create(bool isFromPool = false)
    {
      return ObjectPool.Instance.Fetch(typeof(Main2NetClient_Login), isFromPool) as Main2NetClient_Login;
    }

    [MemoryPackOrder(0)]
    public int RpcId { get; set; }

    [MemoryPackOrder(1)]
    public int OwnerFiberId { get; set; }

    [MemoryPackOrder(2)]
    public TapTapAccessToken tapTapAccessToken;

    public override void Dispose()
    {
      if (!this.IsFromPool)
      {
        return;
      }

      this.RpcId = default;
      this.OwnerFiberId = default;
      this.tapTapAccessToken = default;

      ObjectPool.Instance.Recycle(this);
    }
  }

  [MemoryPackable]
  [Message(ClientMessage.NetClient2Main_Login)]
  public partial class NetClient2Main_Login : MessageObject, IResponse
  {
    public static NetClient2Main_Login Create(bool isFromPool = false)
    {
      return ObjectPool.Instance.Fetch(typeof(NetClient2Main_Login), isFromPool) as NetClient2Main_Login;
    }

    [MemoryPackOrder(0)]
    public int RpcId { get; set; }

    [MemoryPackOrder(1)]
    public int Error { get; set; }

    [MemoryPackOrder(2)]
    public string Message { get; set; }
    [MemoryPackOrder(3)]
    public List<ServerData> serverList;
    // 以下为taptap登录账户信息
    [MemoryPackOrder(4)]
    public string name;
    [MemoryPackOrder(5)]
    public string avatar;
    [MemoryPackOrder(6)]
    public string openId;
    [MemoryPackOrder(7)]
    public string unionId;
    // 账号ID
    [MemoryPackOrder(8)]
    public long accountId;
    [MemoryPackOrder(9)]
    public string accessKey;


    public override void Dispose()
    {
      if (!this.IsFromPool)
      {
        return;
      }

      this.RpcId = default;
      this.Error = default;
      this.Message = default;
      this.serverList = default;

      ObjectPool.Instance.Recycle(this);
    }
  }

  public static class ClientMessage
  {
    public const ushort Main2NetClient_Login = 1001;
    public const ushort NetClient2Main_Login = 1002;
  }
}