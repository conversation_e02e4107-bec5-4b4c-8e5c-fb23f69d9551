﻿using System.Diagnostics;
using MongoDB.Bson.Serialization.Attributes;
using Unity.Mathematics;

namespace MaoYouJi
{
  [ChildOf(typeof(UnitComponent))]
  [DebuggerDisplay("ViewName,nq")]
  public partial class Unit : Entity, IAwake<int>
  {
    public int ConfigId { get; set; } //配置表id

    [BsonElement]
    private float3 position; //坐标

    [BsonIgnore]
    public float3 Position
    {
      get => this.position;
      set
      {
        float3 oldPos = this.position;
        this.position = value;
        EventSystem.Instance.Publish(this.Scene(), new ChangePosition() { Unit = this, OldPos = oldPos });
      }
    }

    [BsonIgnore]
    public float3 Forward
    {
      get => math.mul(this.Rotation, math.forward());
      set => this.Rotation = quaternion.LookRotation(value, math.up());
    }

    [BsonElement]
    private quaternion rotation;

    [BsonIgnore]
    public quaternion Rotation
    {
      get => this.rotation;
      set
      {
        this.rotation = value;
        EventSystem.Instance.Publish(this.Scene(), new ChangeRotation() { Unit = this });
      }
    }

    protected override string ViewName
    {
      get
      {
        return $"{this.GetType().FullName} ({this.Id})";
      }
    }
  }
}