﻿using System.Net;
using System.Net.Sockets;

namespace Mao<PERSON>ou<PERSON>i
{
  public struct NetComponentOnRead
  {
    public Session Session;
    public object Message;
  }

  [ComponentOf(typeof(Scene))]
  public class NetComponent : <PERSON><PERSON>ty, IAwake<IPEndPoint, NetworkProtocol>, IAwake<AddressFamily, NetworkProtocol>, IDestroy, IUpdate
  {
    public AService AService { get; set; }
  }
}