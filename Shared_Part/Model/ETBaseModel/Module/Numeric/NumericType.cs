﻿namespace MaoYouJi
{
  // 这个可弄个配置表生成
  public static class NumericType
  {
    public const int Max = 10000;

    public const int Speed = 1000;
    public const int SpeedBase = Speed * 10 + 1;
    public const int SpeedAdd = Speed * 10 + 2;
    public const int SpeedPct = Speed * 10 + 3;
    public const int SpeedFinalAdd = Speed * 10 + 4;
    public const int SpeedFinalPct = Speed * 10 + 5;

    public const int Hp = 1001;
    public const int HpBase = Hp * 10 + 1;

    public const int MaxHp = 1002;
    public const int MaxHpBase = MaxHp * 10 + 1;
    public const int MaxHpAdd = MaxHp * 10 + 2;
    public const int MaxHpPct = MaxHp * 10 + 3;
    public const int MaxHpFinalAdd = MaxHp * 10 + 4;
    public const int MaxHpFinalPct = MaxHp * 10 + 5;

    public const int AOI = 1003;
    public const int AOIBase = AOI * 10 + 1;
    public const int AOIAdd = AOI * 10 + 2;
    public const int AOIPct = AOI * 10 + 3;
    public const int AOIFinalAdd = AOI * 10 + 4;
    public const int AOIFinalPct = AOI * 10 + 5;
  }
}
