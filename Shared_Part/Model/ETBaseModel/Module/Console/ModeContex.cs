﻿namespace MaoYouJi
{
  [EntitySystemOf(typeof(ModeContex))]
  [FriendOf(typeof(ModeContex))]
  public static partial class ModeContexSystem
  {
    [EntitySystem]
    private static void Awake(this ModeContex self)
    {
      self.Mode = "";
    }

    [EntitySystem]
    private static void Destroy(this ModeContex self)
    {
      self.Mode = "";
    }
  }



  [ComponentOf(typeof(ConsoleComponent))]
  public class ModeContex : <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>roy
  {
    public string Mode = "";
  }
}