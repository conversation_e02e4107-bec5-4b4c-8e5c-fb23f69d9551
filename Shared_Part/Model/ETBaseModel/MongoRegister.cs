﻿using System;
using System.Collections.Generic;
using System.Reflection;
using ConcurrentCollections;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Conventions;
using MongoDB.Bson.Serialization.Serializers;
using TrueSync;
using Unity.Mathematics;

namespace MaoYouJi
{
  public static class MongoRegister
  {
    private static void RegisterStruct<T>() where T : struct
    {
      BsonSerializer.RegisterSerializer(typeof(T), new StructBsonSerialize<T>());
    }

    public static void Init()
    {
      // 清理老的数据
      MethodInfo createSerializerRegistry = typeof(BsonSerializer).GetMethod("CreateSerializerRegistry", BindingFlags.Static | BindingFlags.NonPublic);
      createSerializerRegistry.Invoke(null, Array.Empty<object>());
      MethodInfo registerIdGenerators = typeof(BsonSerializer).GetMethod("RegisterIdGenerators", BindingFlags.Static | BindingFlags.NonPublic);
      registerIdGenerators.Invoke(null, Array.Empty<object>());

      // 注册枚举序列化器，确保所有枚举都以字符串形式存储
      BsonSerializer.RegisterSerializationProvider(new EnumStringSerializationProvider());

      // 自动注册IgnoreExtraElements
      ConventionPack conventionPack = new() { new IgnoreExtraElementsConvention(true) };

      ConventionRegistry.Register("IgnoreExtraElements", conventionPack, type => true);

      // 注册ConcurrentHashSet序列化器
      BsonSerializer.RegisterGenericSerializerDefinition(
          typeof(ConcurrentHashSet<>),
          typeof(ConcurrentHashSetSerializer<>));

      RegisterStruct<float2>();
      RegisterStruct<float3>();
      RegisterStruct<float4>();
      RegisterStruct<quaternion>();
      RegisterStruct<FP>();
      RegisterStruct<TSVector>();
      RegisterStruct<TSVector2>();
      RegisterStruct<TSVector4>();
      RegisterStruct<TSQuaternion>();

      Dictionary<string, Type> types = CodeTypes.Instance.GetTypes();
      foreach (Type type in types.Values)
      {
        if (!type.IsSubclassOf(typeof(Object)))
        {
          continue;
        }

        if (type.IsGenericType)
        {
          continue;
        }

        BsonClassMap.LookupClassMap(type);
      }
    }
  }
}