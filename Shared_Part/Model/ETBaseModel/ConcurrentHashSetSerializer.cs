using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Serializers;
using ConcurrentCollections;
using MaoYouJi;

[EnableClass]
public class ConcurrentHashSetSerializer<T> : SerializerBase<ConcurrentHashSet<T>>
{
  private readonly IBsonSerializer<T> _itemSerializer;

  public ConcurrentHashSetSerializer()
  {
    _itemSerializer = BsonSerializer.LookupSerializer<T>();
  }

  public override ConcurrentHashSet<T> Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
  {
    var reader = context.Reader;
    var result = new ConcurrentHashSet<T>();

    reader.ReadStartArray();
    while (reader.ReadBsonType() != MongoDB.Bson.BsonType.EndOfDocument)
    {
      var item = _itemSerializer.Deserialize(context);
      result.Add(item);
    }
    reader.ReadEndArray();

    return result;
  }

  public override void Serialize(BsonSerializationContext context, BsonSerializationArgs args, ConcurrentHashSet<T> value)
  {
    var writer = context.Writer;
    writer.WriteStartArray();
    foreach (var item in value)
    {
      _itemSerializer.Serialize(context, item);
    }
    writer.WriteEndArray();
  }
}