using System.Collections.Generic;
using MemoryPack;

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.SendChatResp)]
  public partial class SendChatResp : MaoYouOutMessage, ILocationResponse
  {
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.GetMailListResp)]
  public partial class GetMailListResp : MaoYouOutMessage, ILocationResponse
  {
    public List<MailInfo> mailList;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.OptMailResp)]
  public partial class OptMailResp : MaoYouOutMessage, ILocationResponse
  {
  }
}