using System.Collections.Generic;
using MemoryPack;
using MongoDB.Bson;

namespace <PERSON><PERSON>ou<PERSON>i
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.SendChatReq)]
  [ResponseType(nameof(SendChatResp))]
  public partial class SendChatReq : <PERSON>YouInMessage, ILocationRequest
  {
    public ChatType chatType;
    public string content;
    public long targetId;
    public Dictionary<string, Thing> things;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.GetMailListReq)]
  [ResponseType(nameof(GetMailListResp))]
  public partial class GetMailListReq : MaoYouInMessage, ILocationRequest
  {
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.OptMailReq)]
  [ResponseType(nameof(OptMailResp))]
  public partial class OptMailReq : MaoYouInMessage, ILocationRequest
  {
    // 操作类型, delete, read, getThing
    public MailOptType optType;
    // 邮件id
    public List<ObjectId> mailIds;
  }
}