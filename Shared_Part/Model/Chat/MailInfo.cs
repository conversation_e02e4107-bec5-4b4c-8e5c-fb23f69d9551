using System.Collections.Generic;
using MongoDB.Bson;

namespace MaoYouJi
{
  [EnableClass]
  public class MailInfo
  {
    public ObjectId id;
    public FightInfo senderInfo; // 发送者信息
    public long userId; // 用户ID
    public string title; // 邮件标题
    public string content; // 邮件内容
    public long createTime; // 创建时间
    public bool isGet; // 是否已领取
    public bool isRead; // 是否已读
    public bool hasThing; // 是否有物品
    public long coinNum; // 金币数量
    public long catBeanNum; // 猫豆数量
    public long catEyeNum; // 猫眼数量
    public List<Thing> things; // 物品列表
  }
}