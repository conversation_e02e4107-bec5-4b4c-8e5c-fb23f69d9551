using System;
using System.Collections.Generic;
using System.Linq;
using MemoryPack;
using MongoDB.Bson.Serialization.Attributes;

namespace MaoYouJi
{
  [EnableClass]
  [MemoryPackable]
  public partial class AttachStatus : ICloneable
  {
    // 附加给目标的状态
    public AttackState state;
    // 是否为百分比
    [MemoryPackIgnore]
    public bool isPercent = true;
    // 附加给目标状态的结束时间
    public long endTime;
    // 持续时间，0为永久持续
    public long existTime = 0;
    // 状态对应的层数
    public long num;
    // 状态的最大层数
    public long maxNum;
    // 概率
    [MemoryPackIgnore]
    public long probability;
    // 状态对应的数值
    public long val;
    // 状态对应的附加数值
    public long[] subVals;
    // 施加者信息
    public FightInfo addInfo;

    // 获取状态附加的时间
    public int GetStatusAddTime()
    {
      if (existTime == 0 || endTime == 0)
        return 0;
      int addTime = (int)((existTime - (endTime - TimeInfo.Instance.ServerNow())) / 1000);
      if (addTime < 0)
        addTime = 0;
      return addTime;
    }

    public object Clone()
    {
      AttachStatus status = (AttachStatus)MemberwiseClone();
      if (subVals != null)
      {
        status.subVals = (long[])subVals.Clone();
      }
      // FightInfo是值类型，不需要特殊处理
      return status;
    }
  }

  [EnableClass]
  [MemoryPackable]
  public partial class Skill : ICloneable
  {
    // 技能简称
    public SkillIdEnum skillId;
    // 技能类型
    public SkillTypeEnum skillType;
    // 是否激活
    public bool isActive = true;
    // 中文名称
    [MemoryPackIgnore]
    public string name;
    // 等级
    public int level;
    // 伤害
    [MemoryPackIgnore]
    public long damage; // 这个是基础伤害
                        // 伤害类型
    [MemoryPackIgnore]
    public long extraDmg; // 这个是额外伤害，百分比技能等于 damage * 攻击力 + extraDamage
                          // 消耗蓝量
    [MemoryPackIgnore]
    public int expend;
    // 延迟时间
    [MemoryPackIgnore]
    public long delayTime;
    // 冷却时间，单位ms
    [MemoryPackIgnore]
    public long cd;
    // 附加给目标的状态列表
    [MemoryPackIgnore]
    public List<AttachStatus> targetStatus;
    // 附加给自己的状态列表
    [MemoryPackIgnore]
    public List<AttachStatus> selfStatus;
    // 二次释放需要的状态
    [MemoryPackIgnore]
    public AttackState secondUseNeedState = AttackState.None;
    // 释放技能需要的基础职业
    [MemoryPackIgnore]
    public BaseJob needBaseJob = BaseJob.None;
    // 释放技能需要的职业
    [MemoryPackIgnore]
    public MaoJob job = MaoJob.None;
    // 其它的数值列表
    [MemoryPackIgnore]
    public long[] vals;
    // 类型标记
    [MemoryPackIgnore]
    public long typeFlag = 1;
    // 当前经验
    public long nowExp;
    // 最大经验
    public long maxExp;
    // 治疗类型
    [MemoryPackIgnore]
    public CureType cureType = CureType.None;
    // 技能冷却时间
    [MemoryPackIgnore]
    public long coolTime;
    // 目标数量, 0-1表示单体, -1表示全体, 2以上表示攻击多少个目标
    [MemoryPackIgnore]
    public short targetNum;

    // 是否为百分比技能
    public bool isPercent()
    {
      return (typeFlag & 1) == 1;
    }

    // 是否为可以使用的技能
    public bool isCanUse()
    {
      return (typeFlag & 2) == 2;
    }

    // 是否为增加基础技能熟练度
    public bool isAddBaseSkillExp()
    {
      return (typeFlag & 4) == 4;
    }

    public object Clone()
    {
      Skill skill = (Skill)MemberwiseClone();
      if (targetStatus != null)
      {
        skill.targetStatus = targetStatus.Select(status => (AttachStatus)status.Clone()).ToList();
      }
      if (selfStatus != null)
      {
        skill.selfStatus = selfStatus.Select(status => (AttachStatus)status.Clone()).ToList();
      }
      if (vals != null)
      {
        skill.vals = (long[])vals.Clone();
      }
      return skill;
    }
  }
}