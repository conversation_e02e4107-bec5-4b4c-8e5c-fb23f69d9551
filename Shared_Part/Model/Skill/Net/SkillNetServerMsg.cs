using System.Collections.Generic;
using MemoryPack;

namespace <PERSON><PERSON>ouJi
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerUpdateSkillInfoMsg)]
  public partial class ServerUpdateSkillInfoMsg : MaoYouOutMessage, ILocationMessage
  {
    public static ServerUpdateSkillInfoMsg Create(Skill skill)
    {
      ServerUpdateSkillInfoMsg msg = new();
      msg.SkillSimpleInfos.Add(skill);
      return msg;
    }

    public List<Skill> SkillSimpleInfos { get; set; } = new();
  }
}