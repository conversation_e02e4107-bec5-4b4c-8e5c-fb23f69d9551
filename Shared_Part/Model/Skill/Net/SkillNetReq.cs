using System.Collections.Generic;
using MemoryPack;

namespace <PERSON><PERSON>ouJi
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ShowSkillListReq)]
  [ResponseType(nameof(ShowSkillListResp))]
  public partial class ShowSkillListReq : MaoYouInMessage, ILocationRequest
  {
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.LearnSkillReq)]
  [ResponseType(nameof(LearnSkillResp))]
  public partial class LearnSkillReq : MaoYouInMessage, ILocationRequest
  {
    public SkillIdEnum SkillId { get; set; }
    public int Level { get; set; }
  }
}