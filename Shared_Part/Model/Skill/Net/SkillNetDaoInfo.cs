using System.Collections.Generic;
using MemoryPack;

namespace MaoYouJi
{
  [EnableClass]
  [MemoryPackable]
  public partial class SkillComponentDaoInfo
  {
    public Dictionary<SkillIdEnum, Skill> skillMap { get; set; } = new(); // 技能map
    public List<SkillIdEnum> skillIds { get; set; } = new(); // 技能id列表
    public SkillIdEnum[] equipSkills { get; set; } = new SkillIdEnum[12]; // 装备的技能
    public Dictionary<SkillIdEnum, long> skillCool { get; set; } = new(); // 技能冷却
    public long[] quickFoods { get; set; } = new long[10]; // 快捷食物
  }
}