using System.Collections.Generic;
using MemoryPack;

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ShowSkillListResp)]
  public partial class ShowSkillListResp : MaoYouOutMessage, ILocationResponse
  {
    public SkillComponentDaoInfo skillComponentDaoInfo { get; set; } = new();
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.LearnSkillResp)]
  public partial class LearnSkillResp : MaoYouOutMessage, ILocationResponse
  {
  }
}