using System.Collections.Generic;
using MemoryPack;

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientChangeQuickSkillMsg)]
  public partial class ClientChangeQuickSkillMsg : MaoYouInMessage, ILocationMessage
  {
    public List<SkillIdEnum> SkillIds { get; set; } = new();
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientActiveSkillMsg)]
  public partial class ClientActiveSkillMsg : MaoYouInMessage, ILocationMessage
  {
    public SkillIdEnum SkillId { get; set; }
  }
}