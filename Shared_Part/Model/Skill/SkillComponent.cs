using System.Collections.Concurrent;
using System.Collections.Generic;

namespace MaoYouJi
{
  [ComponentOf(typeof(AttackComponent))]
  public class SkillComponent : Entity, IAwake, ISerializeToEntity
  {
    public ConcurrentDictionary<SkillIdEnum, Skill> skillMap { get; set; } = new(); // 技能map
    public List<SkillIdEnum> skillIds { get; set; } = new(); // 技能id列表
    public SkillIdEnum[] equipSkills { get; set; } = new SkillIdEnum[12]; // 装备的技能
    public ConcurrentDictionary<SkillIdEnum, long> skillCool { get; set; } = new(); // 技能冷却
    public long[] quickFoods { get; set; } = new long[10]; // 快捷食物
  }
}