using System.Collections.Generic;

namespace MaoYouJi
{
  // ForbiddenArea类表示一个禁区
  [EnableClass]
  public class ForbiddenArea
  {
    // 原始子区域列表
    public List<List<string>> originSubAreas;
    // 当前子区域列表，使用@Transient注解表示不持久化
    public List<List<string>> nowSubAreas = new();
    // 当前子区域索引，使用@Transient注解表示不持久化
    public int nowSubAreaIndex = 0;
  }
  // DaTaoShaUserInfo类表示用户信息
  [EnableClass]
  public class DaTaoShaUserInfo
  {
    // 用户ID
    public long userId;
    // 进入时间
    public long enterTime;
    // 总击杀数
    public int totalKill = 0;
    // 击杀超过6级的次数
    public int killOver6 = 0;
    // 大逃杀排名
    public int ranking = 100000;
    // 获得奖章数量
    public int jiangZhangCount = 0;
    // 增加的属性信息
    public int addStrength = 0;
    public int addPower = 0;
    public int addQuick = 0;
    public int addMind = 0;
    public int addIq = 0;
  }

  // DaTaoShaAct类继承自BaseAct，表示一个大逃杀活动
  [EnableClass]
  public class DaTaoShaActConf : BaseActConf
  {
    // 停止进入时间，单位为毫秒
    public long stopEnterTime = 10 * 60 * 1000L;
    // 增加禁区的时间间隔，单位为毫秒
    public long addForbiddenInterval = 60 * 1000L;
    // 隔断时间击杀用户，单位为毫秒
    public long killUserInterval = 18 * 1000L;
    // 禁区列表
    public List<ForbiddenArea> forbiddenAreas;
    // 大逃杀活动状态，0：未开始，1：进行中，2：开启击杀
    public volatile int state = 0;
    // 是否预计算子区域，使用@Transient注解表示不持久化
    public bool preCalcSubArea = false;
    // 当前禁区索引，使用@Transient注解表示不持久化
    public int nowForbiddenAreaIndex = 0;
  }
}