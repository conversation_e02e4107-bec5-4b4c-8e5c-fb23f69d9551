namespace MaoYouJi
{
  public enum BonusAddType
  {
    None,
    Add_Hp, //("增加生命值"),
    Add_Sp, //("增加法力值"),
    Add_Attack, //("增加攻击力"),
    Add_Real_Attack, //("增加真实伤害"),
    Add_Defense, //("增加防御力"),
    Add_Magic_Defense, //("增加魔法防御力"),
    Add_Attack_Speed, //("增加攻击速度"),
    Add_Skill_Speed, //("增加施法速度"),
    Add_Crit, //("增加暴击率"),
    Add_Crit_Dmg, //("增加暴击伤害"),
    Add_Hit_Rate, //("增加命中率"),
    Add_Miss_Rate, //("增加闪避率"),
    Add_Strength, //("增加体力"),
    Add_Power, //("增加力量"),
    Add_Quick, //("增加敏捷"),
    Add_Iq, //("增加智力"),
    Add_Mind, //("增加精神"),
    Add_Damage_Reduce, //("增加伤害减免"),
    Add_Damage_Add, //("增加伤害增加"),
  }
  public enum EquipBonusName
  {
    DiKang_LieYan, //("抵抗烈焰"),
  }

  public enum EquipMainAttrType
  {
    None,
    Attack, //("攻击力"),
    RealAttack, //("真实伤害"),
    Crit, //("暴击率"),
    CritDamage, //("暴击伤害"),
    HitRate, //("命中率"),
    MissRate, //("闪避率"),
    Defense, //("防御力"),
    Blood, //("生命值"),
    Blue, //("魔法值"),
    Strength, //("体力"),
    Power, //("力量"),
    Quick, //("敏捷"),
    Iq, //("智力"),
    Mind, //("精神"),
  }
}