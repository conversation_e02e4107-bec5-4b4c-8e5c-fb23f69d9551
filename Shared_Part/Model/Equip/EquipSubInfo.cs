using System.Collections.Generic;
using MemoryPack;

namespace MaoYouJi
{
  [EnableClass]
  public class BonusAddInfo
  {
    public BonusAddType bonusAddType;
    public int addValue;
    public bool isPercent = false;
  }

  [EnableClass]
  public partial class EquipBonus
  {
    // 需要装备的数量
    public int needNum;
    // 套装效果描述
    public EquipBonusName equipBonusName;
    // 套装效果列表
    public List<BonusAddInfo> bonusAddInfoList;
  }

  [EnableClass]
  [MemoryPackable]
  public partial class EquipBonusInfo
  {
    public string id;
    public EquipBonusType equipBonusType;
    public List<EquipPart> equipPartList;
    public List<EquipBonus> equipBonusList;
  }
}