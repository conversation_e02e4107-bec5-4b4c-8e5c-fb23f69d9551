using System;
using MemoryPack;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace MaoYouJi
{
  [EnableClass]
  [MemoryPackable]
  public partial class EquipAttrInfo : ICloneable
  {
    public int minAttack;
    public int maxAttack;
    public int attackRate;
    public int realAttack;
    public int crit;
    public int critDamage;
    public int hitRate;
    public int miss;
    public int defense;
    public int magicDefense;
    public int blood;
    public int blue;
    public int strength;
    public int power;
    public int quick;
    public int iq;
    public int mind;
    public object Clone()
    {
      EquipAttrInfo clone = (EquipAttrInfo)this.MemberwiseClone();
      return clone;
    }
  }

  [EnableClass]
  [MemoryPackable]
  public partial class Equipment : Thing
  {
    [MemoryPackIgnore]
    public string equipName; // 装备名称
    [MemoryPackIgnore]
    public BaseJob baseJob; // 基础职业
    [MemoryPackIgnore]
    public int level; // 装备等级
    [MemoryPackIgnore]
    public EquipPart equipPart; // 装备部位
    [MemoryPackIgnore]
    public WeaponType weaponType; // 武器类型
    [MemoryPackIgnore]
    public EquipAttrInfo baseAttrInfo; // 基础属性
    public EquipAttrInfo nowAttrInfo; // 当前属性
    public int enhanceLevel = 0; // 强化等级
    [MemoryPackIgnore]
    public EquipBonusType equipBonusType; // 套装类型
    [MemoryPackIgnore]
    public long equipTag; // 装备标签
    [MemoryPackIgnore]
    public int useCnt = 50; // 耐久
    public int remainUseCnt = 50; // 剩余耐久
    public int maxGemNum = 0; // 最大宝石数量
    public Material[] gemList = new Material[3]; // 宝石列表

    public bool isTwoHand()
    {
      return (equipTag & 1) == 1;
    }
  }
}