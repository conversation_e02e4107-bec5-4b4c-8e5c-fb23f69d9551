namespace MaoYouJi
{
  public enum TaskPreType
  {
    None,
    Skill_level_Pre, // ("技能等级前置"),
    Relation_level_Pre, // ("关系等级前置"),
  }
  public enum TaskRequireType
  {
    None,
    Kill_Mon_Cond, // ("击杀怪物要求"),
    Get_Thing_Cond, // ("获取物品要求"),
    Use_Thing_Cond, // ("使用物品要求"),
    Sell_Thing_Cond, // ("出售物品要求"),
    Learn_Skill_Cond, // ("学习技能要求"),
    Get_Level_Cond, // ("达到等级要求"),
    Find_Npc_Cond, // ("找到NPC要求");
  }

  public enum TaskSubTypeEnum
  {
    None,
    BiaoChe_Task, // ("镖车任务"),
    ShiMing_Task, // ("使命任务"),
  }

  public enum TaskTypeEnum
  {
    None,
    [EnumDescription("主线任务")]
    Main_Task, // ("主线任务"),
    [EnumDescription("支线任务")]
    Side_Task, // ("支线任务"),
    [EnumDescription("日常任务")]
    Daily_Task, // ("日常任务"),
    [EnumDescription("活动任务")]
    Activity_Task, // ("活动任务"),
    [EnumDescription("职业任务")]
    Job_Task, // ("职业任务"),
    [EnumDescription("其他任务")]
    Other_Task, // ("其他任务");
  }
}