using System.Collections.Concurrent;
using System.Collections.Generic;
using ConcurrentCollections;
using MongoDB.Bson.Serialization.Attributes;

namespace MaoYouJi
{

  [EnableClass]
  public class CacheTaskRequireInfo
  {
    public TaskRequire taskRequire; // 任务需求
    [BsonIgnore]
    public BaseTask task; // 对应的任务
  }

  [ComponentOf(typeof(User))]
  public partial class TaskComponent : Entity, IAwake, ISerializeToEntity
  {
    public ConcurrentHashSet<TaskIdEnum> finishedTasks { get; set; } = new(); // 已完成的任务
    public ConcurrentDictionary<TaskIdEnum, BaseTask> nowTasks { get; set; } = new(); // 进行中的任务
    /** 存储任务需求的映射表，键为需求特征，值为需求信息列表 */
    [BsonIgnore]
    public ConcurrentDictionary<string, List<CacheTaskRequireInfo>> taskRequireMap { get; set; } = new();
    /** 存储怪物掉落信息的映射表，键为怪物类型，值为掉落信息列表 */
    [BsonIgnore]
    public ConcurrentDictionary<MonBaseType, List<MonDropInfo>> monDropMap { get; set; } = new();
  }
}