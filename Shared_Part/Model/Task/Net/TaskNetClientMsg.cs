using MemoryPack;

namespace <PERSON><PERSON><PERSON><PERSON>i
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientStartTaskMsg)]
  public partial class ClientStartTaskMsg : MaoYouInMessage, ILocationMessage
  {
    public TaskIdEnum TaskId { get; set; }
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientGetBiaoChePosMsg)]
  public partial class ClientGetBiaoChePosMsg : MaoYouInMessage, ILocationMessage
  {
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientGiveUpTaskMsg)]
  public partial class ClientGiveUpTaskMsg : <PERSON>YouInMessage, ILocationMessage
  {
    public TaskIdEnum TaskId { get; set; }
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientFillNpcTalkMsg)]
  public partial class ClientFillNpcTalkMsg : MaoYouInMessage, ILocationMessage
  {
    public TaskIdEnum TaskId { get; set; }
    public NpcNameEnum NpcName { get; set; }
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientFinishTaskMsg)]
  public partial class ClientFinishTaskMsg : MaoYouInMessage, ILocationMessage
  {
    public TaskIdEnum TaskId { get; set; }
    public ThingNameEnum SelectThingName { get; set; }
  }
}
