using System.Collections.Generic;
using MemoryPack;

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ShowTaskListResp)]
  public partial class ShowTaskListResp : MaoYouOutMessage, ILocationResponse
  {
    public TaskListDaoInfo TaskListDaoInfo { get; set; } = new();
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.GetBiaoCheTaskResp)]
  public partial class GetBiaoCheTaskResp : MaoYouOutMessage, ILocationResponse
  {
    public List<BaseTask> tasks;
    public int nowCnt;
    public int maxCnt;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.GetShiMingTaskResp)]
  public partial class GetShiMingTaskResp : MaoYouOutMessage, ILocationResponse
  {
    public List<BaseTask> tasks;
    public int nowCnt;
    public int maxCnt;
  }
}
