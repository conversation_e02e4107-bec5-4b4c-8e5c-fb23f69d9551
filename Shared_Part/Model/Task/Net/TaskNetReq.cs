using MemoryPack;

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ShowTaskListReq)]
  [ResponseType(nameof(ShowTaskListResp))]
  public partial class ShowTaskListReq : MaoYouInMessage, ILocationRequest
  {
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.GetBiaoCheTaskReq)]
  [ResponseType(nameof(GetBiaoCheTaskResp))]
  public partial class GetBiaoCheTaskReq : MaoYouInMessage, ILocationRequest
  {
    public NpcNameEnum npcName;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.GetShiMingTaskReq)]
  [ResponseType(nameof(GetShiMingTaskResp))]
  public partial class GetShiMingTaskReq : MaoYouInMessage, ILocationRequest
  {
    public NpcNameEnum npcName;
  }

}