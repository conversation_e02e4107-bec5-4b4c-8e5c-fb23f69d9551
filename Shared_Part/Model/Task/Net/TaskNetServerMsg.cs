using System.Collections.Generic;
using MemoryPack;

namespace <PERSON><PERSON>ouJi
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerUpdateTaskInfoMsg)]
  public partial class ServerUpdateTaskInfoMsg : MaoYouOutMessage, ILocationMessage
  {
    public static ServerUpdateTaskInfoMsg Create(params BaseTask[] tasks)
    {
      ServerUpdateTaskInfoMsg msg = new()
      {
        IsAdd = true,
        TaskInfos = new(tasks)
      };
      return msg;
    }

    public static ServerUpdateTaskInfoMsg CreateRemove(TaskIdEnum taskId)
    {
      ServerUpdateTaskInfoMsg msg = new()
      {
        IsAdd = false,
        TaskId = taskId
      };
      return msg;
    }

    public bool IsAdd { get; set; } = true; // 是否是新增任务
    public List<BaseTask> TaskInfos { get; set; } = new();
    public TaskIdEnum TaskId { get; set; } = TaskIdEnum.None; // 任务ID
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerStartNextTaskMsg)]
  public partial class ServerStartNextTaskMsg : MaoYouOutMessage
  {
    public TaskIdEnum TaskId { get; set; } = TaskIdEnum.None; // 任务ID
  }
}