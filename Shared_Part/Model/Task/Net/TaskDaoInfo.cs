using System.Collections.Concurrent;
using System.Collections.Generic;
using ConcurrentCollections;
using MemoryPack;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson.Serialization.Options;

namespace MaoYouJi
{
  [EnableClass]
  [MemoryPackable]
  public partial class TaskListDaoInfo
  {
    public List<TaskIdEnum> finishedTasks = new(); // 已完成的任务
    [BsonDictionaryOptions(DictionaryRepresentation.ArrayOfArrays)]
    public ConcurrentDictionary<TaskIdEnum, BaseTask> nowTasks = new(); // 进行中的任务
  }
}