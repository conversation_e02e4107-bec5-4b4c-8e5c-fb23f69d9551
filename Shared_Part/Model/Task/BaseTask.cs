
using System;
using System.Collections.Generic;
using MemoryPack;
using MongoDB.Bson;

namespace MaoYouJi
{
  [EnableClass]
  public class TaskGive
  {
    public ThingNameEnum thingName; // 任务给予的物品
    public int num = 1; // 给予的数量，默认为1
  }

  [EnableClass]
  public class TaskReward
  {
    public ThingNameEnum thingName; // 奖励的物品
    public ThingGrade thingGrade; // 奖励的物品品级
    public OwnType ownType; // 奖励物品的拥有类型
    public bool chooseOne = false; // 多选一，默认不多选，多选一填 true
    public int num = 1; // 奖励的数量，默认为1
  }

  [EnableClass]
  public class TaskRequire
  {
    public TaskRequireType requireType; // 完成任务的要求
    public ThingNameEnum thingName; // 任务要求的物品，可以为空
    public MonBaseType monBaseType; // 任务要求的怪物，可以为空
    public NpcNameEnum npcName; // 任务要求的NPC，可以为空
    public SkillIdEnum skillName; // 任务要求的技能，可以为空，但是以上四个里面必须填一个
    public string simpleName; // 任务要求的简单名称，可以为空
    public int totalNum; // 全部数量要求
    public int nowNum; // 当前完成的数量
  }

  [EnableClass]
  public class TaskPreCond
  {
    public TaskPreType preCondType; // 任务前置条件类型
    public SkillIdEnum skillName; // 前置条件技能
    public RelationTypeEnum relationType; // 前置条件关系
    public int preCondNum; // 前置条件数值
  }

  [EnableClass]
  public class MonDropInfo
  {
    public MonBaseType monBaseType; // 怪物类型
    public ThingNameEnum thingName; // 怪物掉落物品
    public long dropProb = 10000; // 物品掉落概率, 万分比，记得最高为10000, 10000代表100%，100代表1%
    public TaskIdEnum taskId; // 任务ID
  }

  [MemoryPackable]
  [EnableClass]
  public partial class BaseTask : ICloneable
  {
    public ObjectId id;
    public TaskIdEnum idEnum; // 任务ID枚举
    [MemoryPackIgnore]
    public long taskEntityId; // 任务实体ID
    [MemoryPackIgnore]
    public string name; // 任务名称
    [MemoryPackIgnore]
    public TaskTypeEnum taskType; // 任务类型
    [MemoryPackIgnore]
    public TaskSubTypeEnum taskSubType; // 任务子类型

    [MemoryPackIgnore]
    public int level; // 任务对应的等级
    [MemoryPackIgnore]
    public int minLevel; // 接任务的最低等级，不设置为任务等级-5
    [MemoryPackIgnore]
    public int maxLevel; // 接任务的最高等级，不设置为没有限制
    [MemoryPackIgnore]
    public BaseJob baseJob; // 基础职业要求
    [MemoryPackIgnore]
    public MaoJob maoJob; // 战斗职业要求
    [MemoryPackIgnore]
    public TaskIdEnum preTask; // 前置任务，如果设置了前置任务则必须完成前置任务才能领取此任务，只在主线中设置
    [MemoryPackIgnore]
    public List<TaskIdEnum> preTasks; // 前置任务，如果设置了前置任务则必须完成前置任务其中的一个才能领取此任务，只在主线中设置
    [MemoryPackIgnore]
    public TaskIdEnum nextTask; // 后置任务，用来开启连环任务，只在主线中设置
    [MemoryPackIgnore]
    public List<TaskIdEnum> nextTasks; // 后置任务，用来开启连环任务，只在主线中设置，后置任务只会开启一个
    [MemoryPackIgnore]
    public List<ThingNameEnum> removeThingBeforeList; // 任务接收后需要移除的物品，只在主线中设置
    [MemoryPackIgnore]
    public List<ThingNameEnum> removeThingAfterList; // 任务完成后需要移除的物品，只在主线中设置
    [MemoryPackIgnore]
    public List<MonDropInfo> monDropInfos; // 指定特定怪物掉落特定物品
    [MemoryPackIgnore]
    public int needSubCoinNum; // 需要消耗的货币数量，默认为0
    [MemoryPackIgnore]
    public string targetMap; // 押送镖车的时候用，目标地图
    [MemoryPackIgnore]
    public string targetPoint; // 押送镖车的时候用，目标点

    [MemoryPackIgnore]
    public NpcNameEnum startNpc; // 开始NPC
    [MemoryPackIgnore]
    public NpcNameEnum endNpc; // 结束NPC
    [MemoryPackIgnore]
    public int coin; // 金币奖励，1银币是100货币，1金币是10000货币
    [MemoryPackIgnore]
    public int exp; // 经验奖励
    [MemoryPackIgnore]
    public List<TaskGive> taskGiveList; // 接受任务时给予的物品
    [MemoryPackIgnore]
    public List<TaskPreCond> preCondLists; // 任务前置条件列表
    public List<TaskRequire> requireLists; // 任务要求
    [MemoryPackIgnore]
    public List<TaskReward> rewardLists; // 任务奖励列表
    [MemoryPackIgnore]
    public SkillIdEnum learnSkill; // 任务完成后学习的技能
    [MemoryPackIgnore]
    public Dictionary<RelationTypeEnum, int> relationLevelExp; // 任务完成后提升的关系经验
    [MemoryPackIgnore]
    public Dictionary<SkillIdEnum, int> skillLevelExp; // 任务完成后提升的技能经验

    public object Clone()
    {
      var baseTask = (BaseTask)MemberwiseClone();

      // 深拷贝引用类型成员
      if (preTasks != null)
      {
        baseTask.preTasks = new List<TaskIdEnum>(preTasks);
      }

      if (nextTasks != null)
      {
        baseTask.nextTasks = new List<TaskIdEnum>(nextTasks);
      }

      if (removeThingBeforeList != null)
      {
        baseTask.removeThingBeforeList = new List<ThingNameEnum>(removeThingBeforeList);
      }

      if (removeThingAfterList != null)
      {
        baseTask.removeThingAfterList = new List<ThingNameEnum>(removeThingAfterList);
      }

      if (monDropInfos != null)
      {
        baseTask.monDropInfos = new List<MonDropInfo>();
        foreach (var info in monDropInfos)
        {
          var newInfo = new MonDropInfo
          {
            monBaseType = info.monBaseType,
            thingName = info.thingName,
            dropProb = info.dropProb,
            taskId = info.taskId
          };
          baseTask.monDropInfos.Add(newInfo);
        }
      }

      if (taskGiveList != null)
      {
        baseTask.taskGiveList = new List<TaskGive>();
        foreach (var give in taskGiveList)
        {
          var newGive = new TaskGive
          {
            thingName = give.thingName,
            num = give.num
          };
          baseTask.taskGiveList.Add(newGive);
        }
      }

      if (preCondLists != null)
      {
        baseTask.preCondLists = new List<TaskPreCond>();
        foreach (var cond in preCondLists)
        {
          var newCond = new TaskPreCond
          {
            preCondType = cond.preCondType,
            skillName = cond.skillName,
            relationType = cond.relationType,
            preCondNum = cond.preCondNum
          };
          baseTask.preCondLists.Add(newCond);
        }
      }

      if (requireLists != null)
      {
        baseTask.requireLists = new List<TaskRequire>();
        foreach (var require in requireLists)
        {
          var newRequire = new TaskRequire
          {
            requireType = require.requireType,
            thingName = require.thingName,
            monBaseType = require.monBaseType,
            npcName = require.npcName,
            skillName = require.skillName,
            simpleName = require.simpleName,
            totalNum = require.totalNum,
            nowNum = require.nowNum
          };
          baseTask.requireLists.Add(newRequire);
        }
      }

      if (rewardLists != null)
      {
        baseTask.rewardLists = new List<TaskReward>();
        foreach (var reward in rewardLists)
        {
          var newReward = new TaskReward
          {
            thingName = reward.thingName,
            thingGrade = reward.thingGrade,
            ownType = reward.ownType,
            chooseOne = reward.chooseOne,
            num = reward.num
          };
          baseTask.rewardLists.Add(newReward);
        }
      }

      if (relationLevelExp != null)
      {
        baseTask.relationLevelExp = new Dictionary<RelationTypeEnum, int>(relationLevelExp);
      }

      if (skillLevelExp != null)
      {
        baseTask.skillLevelExp = new Dictionary<SkillIdEnum, int>(skillLevelExp);
      }

      return baseTask;
    }
  }
}