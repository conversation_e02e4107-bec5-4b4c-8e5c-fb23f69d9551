using MemoryPack;

namespace MaoYouJi
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientBuyMallShopMsg)]
  public partial class ClientBuyMallShopMsg : MaoYouMessage, ILocationMessage
  {
    public string mallShopId;
    public int num;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientBuyComShopMsg)]
  public partial class ClientBuyComShopMsg : MaoYouMessage, ILocationMessage
  {
    public string comShopId;
    public int num;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientGetTiaoSaoShopMsg)]
  public partial class ClientGetTiaoSaoShopMsg : MaoYouMessage, ILocationMessage
  {
    public ThingType thingType; // 物品类型
    public ThingGrade thingGrade; // 物品等级
    public EquipPart equipPart; // 装备部位
    public WeaponType weaponType; // 武器类型
    public int[] levelRange; // 等级范围
    public string thingName; // 物品名称，模糊匹配
    public int sortType; // 排序类型, 0: 价格升序, 1: 价格降序
    public int page; // 页码, 从0开始
    public int pageSize; // 每页数量
    public bool isMyShop = false; // 是否是自己的商品
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientUploadTiaoSaoShopMsg)]
  public partial class ClientUploadTiaoSaoShopMsg : MaoYouMessage, ILocationMessage
  {
    public long thingId; // 物品id
    public int price; // 价格
    public int upDay; // 上架天数
    public int upNum; // 上架数量
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientDeleteTiaoSaoShopMsg)]
  public partial class ClientDeleteTiaoSaoShopMsg : MaoYouMessage, ILocationMessage
  {
    public string tiaoSaoShopId; // 商品id
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientBuyTiaoSaoShopMsg)]
  public partial class ClientBuyTiaoSaoShopMsg : MaoYouMessage, ILocationMessage
  {
    public string tiaoSaoShopId; // 商品id
    public int num; // 购买数量
  }
}