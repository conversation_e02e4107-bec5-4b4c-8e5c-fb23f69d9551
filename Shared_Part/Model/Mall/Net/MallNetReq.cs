using MemoryPack;

namespace <PERSON><PERSON>ouJi
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ShowMallShopListReq)]
  [ResponseType(nameof(ShowMallShopListResp))]
  public partial class ShowMallShopListReq : MaoYouInMessage, ILocationRequest
  {
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.GetComShopListReq)]
  [ResponseType(nameof(GetComShopListResp))]
  public partial class GetComShopListReq : MaoYouInMessage, ILocationRequest
  {
    public string shopName;
    public ComShopType comShopType;
    public RelationTypeEnum relationType;
  }

}