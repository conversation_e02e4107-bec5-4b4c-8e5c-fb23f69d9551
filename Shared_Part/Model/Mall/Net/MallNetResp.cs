using System.Collections.Generic;
using MemoryPack;

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ShowMallShopListResp)]
  public partial class ShowMallShopListResp : MaoYouOutMessage, ILocationResponse
  {
    public List<MallShopInfo> MallShopList { get; set; } = new();
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.GetComShopListResp)]
  public partial class GetComShopListResp : MaoYouOutMessage, ILocationResponse
  {
    public List<ComShopInfo> ComShopList { get; set; } = new();
    public long RelationLevel { get; set; } = 0;
    public long RelationExp { get; set; } = 0;
  }
}