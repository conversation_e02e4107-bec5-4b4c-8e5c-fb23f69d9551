using MemoryPack;
using MongoDB.Bson;

namespace <PERSON><PERSON>ouJi
{
  [EnableClass]
  [MemoryPackable]
  public partial class TiaoSaoSellUserInfo
  {
    public long userId; // 用户id
    public string username; // 用户名
  }

  [EnableClass]
  [MemoryPackable]
  public partial class TiaoSaoShopInfo
  {
    public ObjectId id;
    public string shopName; // 商品名称
    public Thing thing; // 商品
    public int price; // 商品单价
    public TiaoSaoSellUserInfo sellUserInfo; // 卖家信息
    public long uploadTime; // 上架时间
    public long endTime; // 结束时间
  }
}