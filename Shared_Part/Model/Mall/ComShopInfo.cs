using MemoryPack;
using MongoDB.Bson;

namespace MaoYouJi
{
  [EnableClass]
  [MemoryPackable]
  public partial class ComShopInfo
  {
    public ObjectId id;
    public string mapName; // 地图名称
    public ComShopType comShopType; // 商店类型
    public ThingNameEnum thingName; // 商品名称
    public ComShopSubType comShopSubType; // 商品子类型
    public int coinType; // 货币类型, 0: 金币, 1: 关系点数
    public ThingGrade grade; // 商品品质
    public OwnType ownType; // 拥有类型
    public int price; // 价格
    public int shopNum = 1; // 商品数量
    public ShopLimitType shopLimitType; // 商品限购类型
    public RelationTypeEnum relationType; // 关系类型
    public int needMinRelationLevel; // 购买所需最低关系等级
    public int shopLimitNum; // 商品限购数量
    public int nowBuyNum; // 当前购买数量
    public int minLevel; // 购买所需等级
    public bool disable = false; // 是否下架
    public int sort = 0; // 排序
  }
}