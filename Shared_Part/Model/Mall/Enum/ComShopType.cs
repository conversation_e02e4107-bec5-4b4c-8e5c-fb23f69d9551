namespace Mao<PERSON>ou<PERSON><PERSON>
{
  public enum ComShopType
  {
    None, // ("无类型"),
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, // ("道具店"),
    <PERSON><PERSON><PERSON>ei<PERSON><PERSON>, //("装备店")
  }

  public enum ComShopSubType
  {
    None, // ("不限购"),
    Level_10_19, // ("10-19级"),
    Level_20_29, // ("20-29级"),
    Level_30_39, // ("30-39级"),
    Level_40_49, // ("40-49级"),
    Level_50_59, // ("50-59级"),
    Level_60_69, // ("60-69级"),
    Level_70_79, // ("70-79级"),
    Level_80_89, // ("80-89级"),
    Level_90_99, // ("90-99级"),
  }
  public enum MallShopType
  {
    Enhance, //("强化商品"),
    Recover, //("恢复商品"),
    Hu<PERSON>u, //("护符商品"),
    <PERSON>, //("宠物商品"),
    Special, //("特殊商品"),
    <PERSON><PERSON><PERSON>, //("召唤商品"),
    <PERSON><PERSON><PERSON>, //("猫眼商品"),
    <PERSON><PERSON><PERSON>, //("其他商品"),
  }

  public enum ShopLimitType
  {
    None, // ("不限购"),
    Daily_Limit, // ("每日限购"),
    Week_Limit, // ("每周限购"),
    Month_Limit, // ("每月限购"),
    Forever_Limit, // ("永久限购"),
  }
}