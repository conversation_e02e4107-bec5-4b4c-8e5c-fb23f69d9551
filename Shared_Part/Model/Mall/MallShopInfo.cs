using System.Collections.Generic;
using MongoDB.Bson;
using MemoryPack;

namespace MaoYouJi
{
  [EnableClass]
  [MemoryPackable]
  public partial class MallShopInfo
  {
    public ObjectId id;
    public ThingNameEnum thingName; // 商品名称
    public ThingGrade grade; // 商品品质
    public OwnType ownType; // 拥有类型
    public int price; // 价格
    public int shopNum = 1; // 商品数量
    public MallShopType shopType; // 商品类型
    public ShopLimitType shopLimitType; // 商品限购类型
    public int shopLimitNum; // 商品限购数量
    public int nowBuyNum; // 当前购买数量
    public Dictionary<RelationTypeEnum, int> relationLevel; // 购买需要的关系等级
    public bool useCatEye = false; // 是否使用猫眼
    public int minLevel; // 购买所需等级
    public bool disable = false; // 是否下架
    public int sort = 0; // 排序
  }
}