using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;
using ConcurrentCollections;
using MemoryPack;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace MaoYouJi
{
  [ComponentOf]
  public class BagComponent : Entity, IAwake<BagType, int>, ISerializeToEntity
  {
    // 创建读写锁实例
    [MemoryPackIgnore]
    [BsonIgnore]
    public readonly ReaderWriterLockSlim bagIdLock = new ReaderWriterLockSlim();
    public long ownId { get; set; } // 所属用户ID
    public BagType bagType { get; set; } // 包裹类型 
    public long coin { get; set; } = 0; // 钱币数量
    public long catBean { get; set; } = 0; // 猫豆数量
    public long catEye { get; set; } = 0; // 猫眼数量  
    public string name { get; set; } // 名称
    public int capacity { get; set; } = 100; // 背包容量
    public List<long> thingIds { get; set; } = new(); // 物品ID列表
    public ConcurrentDictionary<long, Thing> thingMap { get; set; } = new(); // 物品ID列表
    public ConcurrentDictionary<ThingNameEnum, ConcurrentHashSet<long>> nameIdMap { get; set; } = new(); // 名称到id的映射关系
  }
}