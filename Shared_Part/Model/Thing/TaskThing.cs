using MemoryPack;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace <PERSON>YouJi
{
  [EnableClass]
  [MemoryPackable]
  public partial class TaskThing : Thing
  {
    [MemoryPackIgnore]
    public string useMap; // 使用地图
    [MemoryPackIgnore]
    public string usePoint; //  使用点
    [MemoryPackIgnore]
    public MonBaseType callMon; // 召唤怪物类型
    [MemoryPackIgnore]
    public ThingNameEnum targetThing; // 目标物品，使用后获得的物品

    public override object Clone()
    {
      TaskThing taskThing = (TaskThing)base.Clone();
      return taskThing;
    }
  }
}