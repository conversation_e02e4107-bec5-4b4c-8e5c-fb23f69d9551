using System.Collections.Concurrent;
using System.Collections.Generic;
using MemoryPack;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson.Serialization.Options;

namespace MaoYouJi
{
  [EnableClass]
  [MemoryPackable]
  public partial class BagDaoInfo
  {
    public BagType bagType;
    public long coin = 0; // 钱币数量
    public long catBean = 0; // 猫豆数量
    public long catEye = 0; // 猫眼数量  
    public string name; // 名称
    public int capacity = 100; // 背包容量
    public List<long> thingIds = new(); // 物品ID列表
    [BsonDictionaryOptions(DictionaryRepresentation.ArrayOfArrays)]
    public Dictionary<long, Thing> thingMap = new(); // 物品ID列表
    [BsonDictionaryOptions(DictionaryRepresentation.ArrayOfArrays)]
    public Dictionary<ThingNameEnum, HashSet<long>> nameIdMap = new(); // 名称到id的映射关系
  }
}
