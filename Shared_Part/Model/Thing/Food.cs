using MemoryPack;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace MaoYouJi
{
  [EnableClass]
  [MemoryPackable]
  public partial class Food : Thing
  {
    [MemoryPackIgnore]
    public FoodAddType addType; // 回复的属性类型
    [MemoryPackIgnore]
    public long time; // 维持时间,单位为ms,0为永久
    [MemoryPackIgnore]
    public bool isPercent = false; // 是否为百分比回复

    public override object Clone()
    {
      Food food = (Food)base.Clone();
      // 基本类型不需要特殊处理，MemberwiseClone已经复制了值
      // 如果Food类将来添加引用类型字段，需要在这里处理深拷贝
      return food;
    }
  }
}