using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using Path = System.IO.Path;

namespace MaoYouJi
{
  public static class ProcessHelper
  {
    // 运行一个进程，参数包括可执行文件路径、命令行参数、工作目录和是否等待进程退出
    public static System.Diagnostics.Process Run(string exe, string arguments, string workingDirectory = ".", bool waitExit = false)
    {
      //Log.Debug($"Process Run exe:{exe} ,arguments:{arguments} ,workingDirectory:{workingDirectory}");
      try
      {
        // 根据是否等待进程退出来决定是否重定向标准输出和错误输出
        bool redirectStandardOutput = false;
        bool redirectStandardError = false;
        bool useShellExecute = RuntimeInformation.IsOSPlatform(OSPlatform.Windows);

        if (waitExit)
        {
          redirectStandardOutput = true;
          redirectStandardError = true;
          useShellExecute = false;
        }

        // 设置进程启动信息
        ProcessStartInfo info = new ProcessStartInfo
        {
          FileName = exe,
          Arguments = arguments,
          CreateNoWindow = true,
          UseShellExecute = useShellExecute,
          WorkingDirectory = workingDirectory,
          RedirectStandardOutput = redirectStandardOutput,
          RedirectStandardError = redirectStandardError,
        };

        // 启动进程
        System.Diagnostics.Process process = System.Diagnostics.Process.Start(info);

        // 如果需要等待进程退出，则异步等待
        if (waitExit)
        {
          WaitExitAsync(process).Coroutine();
        }

        return process;
      }
      catch (Exception e)
      {
        // 捕获异常并抛出带有详细信息的异常
        throw new Exception($"dir: {Path.GetFullPath(workingDirectory)}, command: {exe} {arguments}", e);
      }
    }

    // 异步等待进程退出
    private static async ETTask WaitExitAsync(System.Diagnostics.Process process)
    {

#if UNITY || UNITY_EDITOR
      // 如果在Unity环境中，记录进程退出信息
      ETLog.Info($"process exit, exitcode: {process.ExitCode} {process.StandardOutput.ReadToEnd()} {process.StandardError.ReadToEnd()}");
#else
      await process.WaitForExitAsync();
#endif
    }

#if UNITY
    // 扩展方法，异步等待进程退出
    private static async Task WaitForExitAsync(this System.Diagnostics.Process self)
    {
      if (!self.HasExited)
      {
        return;
      }

      try
      {
        self.EnableRaisingEvents = true;
      }
      catch (InvalidOperationException)
      {
        if (self.HasExited)
        {
          return;
        }
        throw;
      }

      var tcs = new TaskCompletionSource<bool>();

      // 事件处理程序，当进程退出时设置任务完成
      void Handler(object s, EventArgs e) => tcs.TrySetResult(true);
      
      self.Exited += Handler;

      try
      {
        if (self.HasExited)
        {
          return;
        }
        await tcs.Task;
      }
      finally
      {
        self.Exited -= Handler;
      }
    }
#endif
  }
}