﻿using System.Text;

namespace MaoYouJi
{
  public static class ByteHelper
  {
    public static string ToHex(this byte b)
    {
      return b.ToString("X2");
    }

    public static string ToHex(this byte[] bytes)
    {
      StringBuilder stringBuilder = new StringBuilder();
      foreach (byte b in bytes)
      {
        stringBuilder.Append(b.ToString("X2"));
      }
      return stringBuilder.ToString();
    }

    public static string ToHex(this byte[] bytes, string format)
    {
      StringBuilder stringBuilder = new StringBuilder();
      foreach (byte b in bytes)
      {
        stringBuilder.Append(b.ToString(format));
      }
      return stringBuilder.ToString();
    }

    public static string ToHex(this byte[] bytes, int offset, int count)
    {
      StringBuilder stringBuilder = new StringBuilder();
      for (int i = offset; i < offset + count; ++i)
      {
        stringBuilder.Append(bytes[i].ToString("X2"));
      }
      return stringBuilder.ToString();
    }

    public static string ToStr(this byte[] bytes)
    {
      return Encoding.Default.GetString(bytes);
    }

    public static string ToStr(this byte[] bytes, int index, int count)
    {
      return Encoding.Default.GetString(bytes, index, count);
    }

    public static string Utf8ToStr(this byte[] bytes)
    {
      return Encoding.UTF8.GetString(bytes);
    }

    public static string Utf8ToStr(this byte[] bytes, int index, int count)
    {
      return Encoding.UTF8.GetString(bytes, index, count);
    }

    public static void WriteTo(this byte[] bytes, int offset, uint num)
    {
      bytes[offset] = (byte)(num & 0xff);
      bytes[offset + 1] = (byte)((num & 0xff00) >> 8);
      bytes[offset + 2] = (byte)((num & 0xff0000) >> 16);
      bytes[offset + 3] = (byte)((num & 0xff000000) >> 24);
    }

    public static void WriteTo(this byte[] bytes, int offset, ActorId num)
    {
      bytes.WriteTo(offset, num.Process);
      bytes.WriteTo(offset + 4, num.Fiber);
      bytes.WriteTo(offset + 8, num.InstanceId);
    }

    public static void WriteTo(this byte[] bytes, int offset, int num)
    {
      bytes[offset] = (byte)(num & 0xff);
      bytes[offset + 1] = (byte)((num & 0xff00) >> 8);
      bytes[offset + 2] = (byte)((num & 0xff0000) >> 16);
      bytes[offset + 3] = (byte)((num & 0xff000000) >> 24);
    }

    public static void WriteTo(this byte[] bytes, int offset, byte num)
    {
      bytes[offset] = num;
    }

    public static void WriteTo(this byte[] bytes, int offset, short num)
    {
      bytes[offset] = (byte)(num & 0xff);
      bytes[offset + 1] = (byte)((num & 0xff00) >> 8);
    }

    public static void WriteTo(this byte[] bytes, int offset, ushort num)
    {
      bytes[offset] = (byte)(num & 0xff);
      bytes[offset + 1] = (byte)((num & 0xff00) >> 8);
    }

    public static unsafe void WriteTo(this byte[] bytes, int offset, long num)
    {
      byte* bPoint = (byte*)&num;
      for (int i = 0; i < sizeof(long); ++i)
      {
        bytes[offset + i] = bPoint[i];
      }
    }

    public static long Hash(this byte[] data, int index, int length)
    {
      const int p = 16777619;
      long hash = 2166136261L;

      for (int i = index; i < index + length; i++)
      {
        hash = (hash ^ data[i]) * p;
      }

      hash += hash << 13;
      hash ^= hash >> 7;
      hash += hash << 3;
      hash ^= hash >> 17;
      hash += hash << 5;
      return hash;
    }
  }
}