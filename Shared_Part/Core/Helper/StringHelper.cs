﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text;

namespace MaoYouJi
{
  public static class StringHelper
  {
    public static IEnumerable<byte> ToBytes(this string str)
    {
      byte[] byteArray = Encoding.Default.GetBytes(str);
      return byteArray;
    }

    public static byte[] ToByteArray(this string str)
    {
      byte[] byteArray = Encoding.Default.GetBytes(str);
      return byteArray;
    }

    public static byte[] ToUtf8(this string str)
    {
      byte[] byteArray = Encoding.UTF8.GetBytes(str);
      return byteArray;
    }

    public static byte[] HexToBytes(this string hexString)
    {
      if (hexString.Length % 2 != 0)
      {
        throw new ArgumentException(String.Format(CultureInfo.InvariantCulture, "The binary key cannot have an odd number of digits: {0}", hexString));
      }

      var hexAsBytes = new byte[hexString.Length / 2];
      for (int index = 0; index < hexAsBytes.Length; index++)
      {
        string byteValue = "";
        byteValue += hexString[index * 2];
        byteValue += hexString[index * 2 + 1];
        hexAsBytes[index] = byte.Parse(byteValue, NumberStyles.HexNumber, CultureInfo.InvariantCulture);
      }
      return hexAsBytes;
    }

    public static string Fmt(this string text, params object[] args)
    {
      return string.Format(text, args);
    }

    public static string ListToString<T>(this List<T> list)
    {
      StringBuilder sb = new StringBuilder();
      foreach (T t in list)
      {
        sb.Append(t);
        sb.Append(",");
      }
      return sb.ToString();
    }

    public static string ArrayToString<T>(this T[] args)
    {
      if (args == null)
      {
        return "";
      }

      string argStr = " [";
      for (int arrIndex = 0; arrIndex < args.Length; arrIndex++)
      {
        argStr += args[arrIndex];
        if (arrIndex != args.Length - 1)
        {
          argStr += ", ";
        }
      }

      argStr += "]";
      return argStr;
    }

    public static string ArrayToString<T>(this T[] args, int index, int count)
    {
      if (args == null)
      {
        return "";
      }

      string argStr = " [";
      for (int arrIndex = index; arrIndex < count + index; arrIndex++)
      {
        argStr += args[arrIndex];
        if (arrIndex != args.Length - 1)
        {
          argStr += ", ";
        }
      }

      argStr += "]";
      return argStr;
    }
  }
}