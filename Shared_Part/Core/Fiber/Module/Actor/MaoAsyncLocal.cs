using System.Threading;

namespace Mao<PERSON>ouJi
{
  [EnableClass]
  public class Mao<PERSON>yncParams
  {
    public int RequestId;
    public string AttackId;
  }

  public static class MaoAsyncLocal
  {

    [StaticField]
    private static AsyncLocal<MaoAsyncParams> _asyncLocal = new();
    private static MaoAsyncParams GetAsyncLocal()
    {
      _asyncLocal.Value ??= new MaoAsyncParams();
      return _asyncLocal.Value;
    }
    public static int RequestId
    {
      get
      {
        return GetAsyncLocal().RequestId;
      }
      set
      {
        GetAsyncLocal().RequestId = value;
      }
    }

    public static string AttackId
    {
      get
      {
        return GetAsyncLocal().AttackId;
      }
      set
      {
        GetAsyncLocal().AttackId = value;
      }
    }
  }
}