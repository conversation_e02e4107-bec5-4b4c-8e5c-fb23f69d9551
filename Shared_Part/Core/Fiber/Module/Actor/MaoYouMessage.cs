using MemoryPack;

namespace <PERSON><PERSON>ouJi
{
  [EnableClass]
  [MemoryPackable(GenerateType.NoGenerate)]
  public abstract partial class MaoYouMessage : MessageObject
  {
    public int RpcId { get; set; }
    public int RequestId { get; set; }
    public long UserId { get; set; }
    public long MsgTime { get; set; }
  }

  [EnableClass]
  [MemoryPackable(GenerateType.NoGenerate)]
  public abstract partial class MaoYouInMessage : MaoYouMessage
  {
  }

  [EnableClass]
  [MemoryPackable(GenerateType.NoGenerate)]
  public abstract partial class MaoYouOutMessage : MaoYouMessage
  {
    public int Error { get; set; }
    public string Message { get; set; }
    public string showMessage { get; set; }

    public void SetError(string msg)
    {
      Error = ErrorCore.ERR_Show_Msg;
      showMessage = msg;
    }
  }
}