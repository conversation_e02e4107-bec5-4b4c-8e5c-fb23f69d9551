using System;

namespace MaoYouJi
{
  public static class MessageHelper
  {
    public static IResponse CreateResponse(Type requestType, int rpcId, int error)
    {
      Type responseType = OpcodeType.Instance.GetResponseType(requestType);
      IResponse response = (IResponse)ObjectPool.Instance.Fetch(responseType);
      response.Error = error;
      response.RpcId = rpcId;
      return response;
    }

    public static IResponse CreateResponse(Type requestType, int rpcId, string showMessage)
    {
      Type responseType = OpcodeType.Instance.GetResponseType(requestType);
      IResponse preResp = (IResponse)ObjectPool.Instance.Fetch(responseType);
      MaoYouOutMessage response = preResp as MaoYouOutMessage;
      if (response == null)
      {
        ETLog.Error($"response type is not MaoYouOutMessage: {requestType.FullName} {responseType.FullName}");
        preResp.Error = ErrorCore.ERR_RpcFail;
        preResp.RpcId = rpcId;
        preResp.Message = "服务器内部错误";
        return preResp;
      }
      response.Error = ErrorCore.ERR_Show_Msg;
      response.RpcId = rpcId;
      response.showMessage = showMessage;
      return preResp;
    }
  }
}