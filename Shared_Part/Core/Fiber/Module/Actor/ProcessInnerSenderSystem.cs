﻿using System;
using System.Collections.Generic;
using System.IO;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(ProcessInnerSender))]
  [FriendOf(typeof(ProcessInnerSender))]
  public static partial class ProcessInnerSenderSystem
  {
    [EntitySystem]
    private static void Destroy(this ProcessInnerSender self)
    {
      Fiber fiber = self.Fiber();
      MessageQueue.Instance.RemoveQueue(fiber.Id);
    }

    [EntitySystem]
    private static void Awake(this ProcessInnerSender self)
    {
      Fiber fiber = self.Fiber();
      MessageQueue.Instance.AddQueue(fiber.Id);
    }

    [EntitySystem]
    private static void Update(this ProcessInnerSender self)
    {
      self.list.Clear();
      Fiber fiber = self.Fiber();
      MessageQueue.Instance.Fetch(fiber.Id, 1000, self.list);

      foreach (MessageInfo actorMessageInfo in self.list)
      {
        self.HandleMessage(fiber, actorMessageInfo);
      }
    }
    // 这里是每个纤程处理消息的入口，有必要可以在此拦截
    private static void HandleMessage(this ProcessInnerSender self, Fiber fiber, in MessageInfo messageInfo)
    {
      if (messageInfo.MessageObject is MaoYouMessage maoYouMessage)
      {
        MaoAsyncLocal.RequestId = maoYouMessage.RequestId;
      }
      else
      {
        MaoAsyncLocal.RequestId = 0;
      }

      if (messageInfo.MessageObject is IResponse response)
      {
        self.HandleIActorResponse(response);
        return;
      }

      ActorId actorId = messageInfo.ActorId;
      MessageObject message = messageInfo.MessageObject;

      MailBoxComponent mailBoxComponent = self.Fiber().Mailboxes.Get(actorId.InstanceId);
      if (mailBoxComponent == null)
      {
        ETLog.Warning($"actor not found mailbox, from: {actorId} current: {fiber.Address} {message}");
        if (message is IRequest request)
        {
          IResponse resp = MessageHelper.CreateResponse(request.GetType(), request.RpcId, ErrorCore.ERR_NotFoundActor);
          self.Reply(actorId.Address, resp);
        }
        return;
      }
      mailBoxComponent.Add(actorId.Address, message);
    }

    private static void HandleIActorResponse(this ProcessInnerSender self, IResponse response)
    {
      if (!self.requestCallback.Remove(response.RpcId, out MessageSenderStruct actorMessageSender))
      {
        return;
      }
      Run(actorMessageSender, response);
    }

    private static void Run(MessageSenderStruct self, IResponse response)
    {
      if (response.Error == ErrorCore.ERR_MessageTimeout)
      {
        self.SetException(new RpcException(response.Error, $"Rpc error: request, 注意Actor消息超时，请注意查看是否死锁或者没有reply: actorId: {self.ActorId} {self.RequestType.FullName}, response: {response}"));
        return;
      }

      if (self.NeedException && ErrorCore.IsRpcNeedThrowException(response.Error))
      {
        self.SetException(new RpcException(response.Error, $"Rpc error: actorId: {self.ActorId} request: {self.RequestType.FullName}, response: {response}"));
        return;
      }

      self.SetResult(response);
    }

    public static void Reply(this ProcessInnerSender self, Address fromAddress, IResponse message)
    {
      self.SendInner(new ActorId(fromAddress, 0), (MessageObject)message);
    }

    public static void Send(this ProcessInnerSender self, ActorId actorId, IMessage message)
    {
      self.SendInner(actorId, (MessageObject)message);
    }

    private static bool SendInner(this ProcessInnerSender self, ActorId actorId, MessageObject message)
    {
      Fiber fiber = self.Fiber();

      // 如果发向同一个进程，则扔到消息队列中
      if (actorId.Process != fiber.Process)
      {
        throw new Exception($"actor inner process diff: {actorId.Process} {fiber.Process}");
      }

      if (message is MaoYouInMessage maoYouInMessage)
      {
        maoYouInMessage.RequestId = MaoAsyncLocal.RequestId;
      }

      return MessageQueue.Instance.Send(fiber.Address, actorId, message);
    }

    private static int GetRpcId(this ProcessInnerSender self)
    {
      return ++self.RpcId;
    }

    public static async ETTask<IResponse> Call(
            this ProcessInnerSender self,
            ActorId actorId,
            IRequest request,
            bool needException = true
    )
    {
      int rpcId = self.GetRpcId();
      request.RpcId = rpcId;

      if (actorId == default)
      {
        throw new Exception($"actor id is 0: {request}");
      }

      Fiber fiber = self.Fiber();
      if (fiber.Process != actorId.Process)
      {
        throw new Exception($"actor inner process diff: {actorId.Process} {fiber.Process}");
      }

      Type requestType = request.GetType();

      IResponse response;
      if (!self.SendInner(actorId, (MessageObject)request))  // 纤程不存在
      {
        response = MessageHelper.CreateResponse(requestType, rpcId, ErrorCore.ERR_NotFoundActor);
        return response;
      }

      MessageSenderStruct messageSenderStruct = new(actorId, requestType, needException);
      self.requestCallback.Add(rpcId, messageSenderStruct);

      async ETTask Timeout()
      {
        await fiber.Root.GetComponent<TimerComponent>().WaitAsync(ProcessInnerSender.TIMEOUT_TIME);

        if (!self.requestCallback.Remove(rpcId, out MessageSenderStruct action))
        {
          return;
        }

        if (needException)
        {
          action.SetException(new Exception($"actor sender timeout: {requestType.FullName}"));
        }
        else
        {
          IResponse response = MessageHelper.CreateResponse(requestType, rpcId, ErrorCore.ERR_Timeout);
          action.SetResult(response);
        }
      }

      Timeout().Coroutine();

      long beginTime = TimeInfo.Instance.ServerFrameTime();

      response = await messageSenderStruct.Wait();

      long endTime = TimeInfo.Instance.ServerFrameTime();

      long costTime = endTime - beginTime;
      if (costTime > 200)
      {
        ETLog.Warning($"actor rpc time > 200: {costTime} {requestType.FullName}");
      }

      return response;
    }
  }
}