﻿using System.Collections.Generic;

namespace MaoYouJi
{
  // 定义计时器类型的枚举
  public enum TimerType
  {
    None,            // 无类型
    OnceTimer,       // 一次性计时器
    OnceWaitTimer,   // 一次性等待计时器
    RepeatedTimer,   // 重复计时器
  }

  // 定义计时器动作的结构体
  public struct TimerAction
  {
    // 构造函数初始化计时器动作
    public TimerAction(TimerType timerClass, long startTime, long time, int type, object obj)
    {
      this.TimerClass = timerClass; // 计时器类型
      this.StartTime = startTime;   // 开始时间
      this.Object = obj;            // 关联的对象
      this.Time = time;             // 持续时间
      this.Type = type;             // 类型标识
    }

    public TimerType TimerClass; // 计时器类型
    public int Type;              // 类型标识
    public object Object;         // 关联的对象
    public long StartTime;        // 开始时间
    public long Time;             // 持续时间
  }

  // 定义计时器回调的结构体
  public struct TimerCallback
  {
    public object Args; // 回调参数
  }

  // 定义计时器组件系统
  [EntitySystemOf(typeof(TimerComponent))]
  public static partial class TimerComponentSystem
  {
    // 初始化计时器组件
    [EntitySystem]
    private static void Awake(this TimerComponent self)
    {
    }

    // 更新计时器组件
    [EntitySystem]
    private static void Update(this TimerComponent self)
    {
      // 如果没有计时器ID，直接返回
      if (self.timeId.Count == 0)
      {
        return;
      }

      long timeNow = self.GetNow(); // 获取当前时间

      // 如果当前时间小于最小时间，直接返回
      if (timeNow < self.minTime)
      {
        return;
      }

      // 遍历所有计时器ID
      foreach (var kv in self.timeId)
      {
        long k = kv.Key;
        if (k > timeNow)
        {
          self.minTime = k; // 更新最小时间
          break;
        }

        self.timeOutTime.Enqueue(k); // 将超时的时间加入队列
      }

      // 处理超时的计时器
      while (self.timeOutTime.Count > 0)
      {
        long time = self.timeOutTime.Dequeue();
        var list = self.timeId[time];
        for (int i = 0; i < list.Length; ++i)
        {
          long timerId = list[i];
          self.timeOutTimerIds.Enqueue(timerId); // 将超时的计时器ID加入队列
        }
        self.timeId.Remove(time); // 移除已处理的时间
      }

      // 如果没有计时器ID，重置最小时间
      if (self.timeId.Count == 0)
      {
        self.minTime = long.MaxValue;
      }

      // 处理超时的计时器ID
      while (self.timeOutTimerIds.Count > 0)
      {
        long timerId = self.timeOutTimerIds.Dequeue();

        if (!self.timerActions.Remove(timerId, out TimerAction timerAction))
        {
          continue;
        }

        self.Run(timerId, ref timerAction); // 执行计时器动作
      }
    }

    // 获取新的计时器ID
    private static long GetId(this TimerComponent self)
    {
      return ++self.idGenerator;
    }

    // 获取当前时间
    private static long GetNow(this TimerComponent self)
    {
      return TimeInfo.Instance.ServerFrameTime();
    }

    // 执行计时器动作
    private static void Run(this TimerComponent self, long timerId, ref TimerAction timerAction)
    {
      switch (timerAction.TimerClass)
      {
        case TimerType.OnceTimer:
          {
            // 调用事件系统
            EventSystem.Instance.Invoke(timerAction.Type, new TimerCallback() { Args = timerAction.Object });
            break;
          }
        case TimerType.OnceWaitTimer:
          {
            // 设置任务结果
            ETTask tcs = timerAction.Object as ETTask;
            tcs.SetResult();
            break;
          }
        case TimerType.RepeatedTimer:
          {
            // 重新添加计时器
            long timeNow = self.GetNow();
            timerAction.StartTime = timeNow;
            self.AddTimer(timerId, ref timerAction);
            EventSystem.Instance.Invoke(timerAction.Type, new TimerCallback() { Args = timerAction.Object });
            break;
          }
      }
    }

    // 添加计时器
    private static void AddTimer(this TimerComponent self, long timerId, ref TimerAction timer)
    {
      long tillTime = timer.StartTime + timer.Time;
      self.timeId.Add(tillTime, timerId); // 添加到时间ID映射
      self.timerActions.Add(timerId, timer); // 添加到计时器动作映射
      if (tillTime < self.minTime)
      {
        self.minTime = tillTime; // 更新最小时间
      }
    }

    // 移除计时器
    public static bool Remove(this TimerComponent self, ref long id)
    {
      long i = id;
      id = 0;
      return self.Remove(i);
    }

    // 移除计时器
    private static bool Remove(this TimerComponent self, long id)
    {
      if (id == 0)
      {
        return false;
      }

      if (!self.timerActions.Remove(id, out TimerAction _))
      {
        return false;
      }
      return true;
    }

    // 异步等待直到指定时间
    public static async ETTask WaitTillAsync(this TimerComponent self, long tillTime, ETCancellationToken cancellationToken = null)
    {
      long timeNow = self.GetNow();
      if (timeNow >= tillTime)
      {
        return;
      }

      ETTask tcs = ETTask.Create(true);
      long timerId = self.GetId();
      TimerAction timer = new(TimerType.OnceWaitTimer, timeNow, tillTime - timeNow, 0, tcs);
      self.AddTimer(timerId, ref timer);

      void CancelAction()
      {
        if (self.Remove(timerId))
        {
          tcs.SetResult();
        }
      }

      try
      {
        cancellationToken?.Add(CancelAction);
        await tcs;
      }
      finally
      {
        cancellationToken?.Remove(CancelAction);
      }
    }

    // 异步等待一帧
    public static async ETTask WaitFrameAsync(this TimerComponent self, ETCancellationToken cancellationToken = null)
    {
      await self.WaitAsync(1, cancellationToken);
    }

    // 异步等待指定时间
    public static async ETTask WaitAsync(this TimerComponent self, long time, ETCancellationToken cancellationToken = null)
    {
      if (time == 0)
      {
        return;
      }

      long timeNow = self.GetNow();

      ETTask tcs = ETTask.Create(true);
      long timerId = self.GetId();
      TimerAction timer = new(TimerType.OnceWaitTimer, timeNow, time, 0, tcs);
      self.AddTimer(timerId, ref timer);

      void CancelAction()
      {
        if (self.Remove(timerId))
        {
          tcs.SetResult();
        }
      }

      try
      {
        cancellationToken?.Add(CancelAction);
        await tcs;
      }
      finally
      {
        cancellationToken?.Remove(CancelAction);
      }
    }

    // 创建一次性计时器
    public static long NewOnceTimer(this TimerComponent self, long tillTime, int type, object args)
    {
      long timeNow = self.GetNow();
      if (tillTime < timeNow)
      {
        ETLog.Error($"new once time too small: {tillTime}");
      }
      long timerId = self.GetId();
      TimerAction timer = new(TimerType.OnceTimer, timeNow, tillTime - timeNow, type, args);
      self.AddTimer(timerId, ref timer);
      return timerId;
    }

    // 创建帧计时器
    public static long NewFrameTimer(this TimerComponent self, int type, object args)
    {
#if DOTNET
      return self.NewRepeatedTimerInner(100, type, args);
#else
      return self.NewRepeatedTimerInner(0, type, args);
#endif
    }

    // 创建重复计时器
    private static long NewRepeatedTimerInner(this TimerComponent self, long time, int type, object args)
    {
#if DOTNET
      if (time < 100)
      {
        throw new Exception($"repeated timer < 100, timerType: time: {time}");
      }
#endif

      long timeNow = self.GetNow();
      long timerId = self.GetId();
      TimerAction timer = new(TimerType.RepeatedTimer, timeNow, time, type, args);

      // 每帧执行的不用加到timerId中，防止遍历
      self.AddTimer(timerId, ref timer);
      return timerId;
    }

    // 创建重复计时器
    public static long NewRepeatedTimer(this TimerComponent self, long time, int type, object args)
    {
      if (time < 100)
      {
        ETLog.Error($"time too small: {time}");
        return 0;
      }

      return self.NewRepeatedTimerInner(time, type, args);
    }
  }

  // 定义计时器组件类
  [ComponentOf(typeof(Scene))]
  public class TimerComponent : Entity, IAwake, IUpdate
  {
    // 时间ID映射，key: time, value: timer id
    public readonly NativeCollection.MultiMap<long, long> timeId = new(1000);

    // 超时时间队列
    public readonly Queue<long> timeOutTime = new();

    // 超时计时器ID队列
    public readonly Queue<long> timeOutTimerIds = new();

    // 计时器动作映射
    public readonly Dictionary<long, TimerAction> timerActions = new();

    // ID生成器
    public long idGenerator;

    // 记录最小时间，不用每次都去MultiMap取第一个值
    public long minTime = long.MaxValue;
  }
}