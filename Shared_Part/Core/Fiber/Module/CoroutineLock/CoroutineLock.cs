using System;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(CoroutineLock))]
  public static partial class CoroutineLockSystem
  {
    [EntitySystem]
    private static void Awake(this CoroutineLock self, int type, long k, int count)
    {
      self.type = type;
      self.key = k;
      self.level = count;
    }

    [EntitySystem]
    private static void Destroy(this CoroutineLock self)
    {
      self.Scene<CoroutineLockComponent>().RunNextCoroutine(self.type, self.key, self.level + 1);
      self.type = CoroutineLockType.None;
      self.key = 0;
      self.level = 0;
    }
  }

  [ChildOf(typeof(CoroutineLockQueue))]
  public class CoroutineLock : Entity, IAwake<int, long, int>, IDestroy
  {
    public int type;
    public long key;
    public int level;
  }
}