﻿using System.Collections.Concurrent;

namespace MaoYouJi
{
  public class Mailboxes
  {
    private readonly ConcurrentDictionary<long, MailBoxComponent> mailboxes = new();

    public void Add(MailBoxComponent mailBoxComponent)
    {
      this.mailboxes.TryAdd(mailBoxComponent.Parent.InstanceId, mailBoxComponent);
    }

    public void Remove(long instanceId)
    {
      this.mailboxes.TryRemove(instanceId, out _);
    }

    public MailBoxComponent Get(long instanceId)
    {
      this.mailboxes.TryGetValue(instanceId, out MailBoxComponent entity);
      return entity;
    }

    public long Count
    {
      get
      {
        return this.mailboxes.Count;
      }
    }
  }
}