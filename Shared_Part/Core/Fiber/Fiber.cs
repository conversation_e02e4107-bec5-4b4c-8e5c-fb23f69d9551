﻿using System;
using System.Collections.Generic;
using System.Threading;

namespace MaoYouJi
{
  // FiberHelper类提供了一些辅助方法
  public static class FiberHelper
  {
    // 获取ActorId的方法
    public static ActorId GetActorId(this Entity self)
    {
      Fiber root = self.Fiber();
      return new ActorId(root.Process, root.Id, self.InstanceId);
    }
  }

  // Fiber类实现了IDisposable接口
  public class Fiber : IDisposable
  {
    // 该字段只能框架使用，绝对不能改成public，改了后果自负
    [StaticField]
    [ThreadStatic]
    internal static Fiber Instance; // 当前线程的Fiber实例

    public bool IsDisposed; // 标识Fiber是否已被释放

    public int Id; // Fiber的唯一标识符

    public int Zone; // Fiber所属的区域

    public Scene Root { get; } // Fiber的根场景

    // 获取Fiber的地址
    public Address Address
    {
      get
      {
        return new Address(this.Process, this.Id);
      }
    }

    // 获取当前进程的ID
    public int Process
    {
      get
      {
        return Options.Instance.Process;
      }
    }

    public EntitySystem EntitySystem { get; } // 实体系统
    public Mailboxes Mailboxes { get; private set; } // 邮箱系统
    public ThreadSynchronizationContext ThreadSynchronizationContext { get; } // 线程同步上下文
    public ILog Log { get; } // 日志接口

    private readonly Queue<ETTask> frameFinishTasks = new(); // 帧结束任务队列

    // Fiber的构造函数
    internal Fiber(int id, int zone, SceneType sceneType, string name)
    {
      this.Id = id;
      this.Zone = zone;
      this.EntitySystem = new EntitySystem();
      this.Mailboxes = new Mailboxes();
      this.ThreadSynchronizationContext = new ThreadSynchronizationContext();
#if UNITY || UNITY_EDITOR
      this.Log = Logger.Instance.Log; // Unity环境下使用Logger
#else
      this.Log = new NLogger(sceneType.ToString(), this.Process, this.Id); // 非Unity环境下使用NLogger
#endif
      this.Root = new Scene(this, id, 1, sceneType, name); // 初始化根场景
    }

    // 更新方法
    internal void Update()
    {
      try
      {
        this.EntitySystem.Update(); // 更新实体系统
      }
      catch (Exception e)
      {
        this.Log.Error(e); // 捕获异常并记录错误
      }
    }

    // 延迟更新方法
    internal void LateUpdate()
    {
      try
      {
        this.EntitySystem.LateUpdate(); // 延迟更新实体系统
        FrameFinishUpdate(); // 更新帧结束任务

        this.ThreadSynchronizationContext.Update(); // 更新线程同步上下文
      }
      catch (Exception e)
      {
        Log.Error(e); // 捕获异常并记录错误
      }
    }

    // 等待帧结束
    public async ETTask WaitFrameFinish()
    {
      ETTask task = ETTask.Create(true); // 创建一个新的ETTask
      this.frameFinishTasks.Enqueue(task); // 将任务加入队列
      await task; // 等待任务完成
    }

    // 更新帧结束任务
    private void FrameFinishUpdate()
    {
      while (this.frameFinishTasks.Count > 0)
      {
        ETTask task = this.frameFinishTasks.Dequeue(); // 从队列中取出任务
        task.SetResult(); // 设置任务结果
      }
    }

    // 释放资源
    public void Dispose()
    {
      if (this.IsDisposed)
      {
        return; // 如果已经释放，直接返回
      }
      this.IsDisposed = true; // 标记为已释放

      this.Root.Dispose(); // 释放根场景
    }
  }
}