﻿using System;
using System.Collections.Generic;

namespace MaoYouJi
{
  // 实体系统类，负责管理实体的更新和后更新操作
  public class EntitySystem
  {
    // 队列数组，用于存储不同类型的实体引用
    private readonly Queue<EntityRef<Entity>>[] queues = new Queue<EntityRef<Entity>>[InstanceQueueIndex.Max];

    // 构造函数，初始化队列数组
    public EntitySystem()
    {
      for (int i = 0; i < this.queues.Length; i++)
      {
        this.queues[i] = new Queue<EntityRef<Entity>>();
      }
    }

    // 注册系统方法，将实体注册到相应的队列中
    public virtual void RegisterSystem(Entity component)
    {
      Type type = component.GetType();

      // 获取实体类型对应的系统
      TypeSystems.OneTypeSystems oneTypeSystems = EntitySystemSingleton.Instance.TypeSystems.GetOneTypeSystems(type);
      if (oneTypeSystems == null)
      {
        return;
      }
      // 遍历队列标志，决定是否将实体加入队列
      for (int i = 0; i < oneTypeSystems.QueueFlag.Length; ++i)
      {
        if (!oneTypeSystems.QueueFlag[i])
        {
          continue;
        }
        this.queues[i].Enqueue(component);
      }
    }

    // 更新方法，处理更新队列中的实体
    public void Update()
    {
      Queue<EntityRef<Entity>> queue = this.queues[InstanceQueueIndex.Update];
      int count = queue.Count;
      while (count-- > 0)
      {
        Entity component = queue.Dequeue();
        if (component == null || component.IsDisposed || component is not IUpdate)
        {
          continue;
        }

        try
        {
          // 获取实体的更新系统
          List<SystemObject> iUpdateSystems = EntitySystemSingleton.Instance.TypeSystems.GetSystems(component.GetType(), typeof(IUpdateSystem));
          if (iUpdateSystems == null)
          {
            continue;
          }

          // 将实体重新加入队列
          queue.Enqueue(component);

          // 执行每个更新系统
          foreach (IUpdateSystem iUpdateSystem in iUpdateSystems)
          {
            try
            {
              iUpdateSystem.Run(component);
            }
            catch (Exception e)
            {
              ETLog.Error(e);
            }
          }
        }
        catch (Exception e)
        {
          throw new Exception($"entity system update fail: {component.GetType().FullName}", e);
        }
      }
    }

    // 后更新方法，处理后更新队列中的实体
    public void LateUpdate()
    {
      Queue<EntityRef<Entity>> queue = this.queues[InstanceQueueIndex.LateUpdate];
      int count = queue.Count;
      while (count-- > 0)
      {
        Entity component = queue.Dequeue();
        if (component == null || component.IsDisposed || component is not ILateUpdate)
        {
          continue;
        }

        // 获取实体的后更新系统
        List<SystemObject> iLateUpdateSystems = EntitySystemSingleton.Instance.TypeSystems.GetSystems(component.GetType(), typeof(ILateUpdateSystem));
        if (iLateUpdateSystems == null)
        {
          continue;
        }

        // 将实体重新加入队列
        queue.Enqueue(component);

        // 执行每个后更新系统
        foreach (ILateUpdateSystem iLateUpdateSystem in iLateUpdateSystems)
        {
          try
          {
            iLateUpdateSystem.Run(component);
          }
          catch (Exception e)
          {
            ETLog.Error(e);
          }
        }
      }
    }
  }
}