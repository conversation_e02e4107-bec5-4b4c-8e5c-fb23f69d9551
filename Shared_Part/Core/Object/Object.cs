﻿using MongoDB.Bson;

namespace MaoYouJi
{
  [EnableClass]
  public abstract class Object
  {
    public override string ToString()
    {
      // 这里不能用MongoHelper.ToJson，因为单步调试会调用ToString来显示数据
      // 如果MongoHelper.ToJson会调用BeginInit,就出大事了
      // return MongoHelper.ToJson(this);
      return ((object)this).ToJson();
    }

    public string ToJson()
    {
      return MongoHelper.ToJson(this);
    }

    public byte[] ToBson()
    {
      return MongoHelper.Serialize(this);
    }
  }
}