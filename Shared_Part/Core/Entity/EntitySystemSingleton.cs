using System;
using System.Collections.Generic;

namespace MaoYouJi
{
  // 该类是一个单例模式的实体系统管理器，负责管理和调用不同类型的系统。
  [Code]
  public class EntitySystemSingleton : Singleton<EntitySystemSingleton>, ISingletonAwake
  {
    // 用于存储不同类型系统的集合
    public TypeSystems TypeSystems { get; private set; }

    // 初始化方法，创建TypeSystems实例并注册所有带有EntitySystemAttribute的类型
    public void Awake()
    {
      this.TypeSystems = new TypeSystems(InstanceQueueIndex.Max);

      foreach (Type type in CodeTypes.Instance.GetTypes(typeof(EntitySystemAttribute)))
      {
        ETLog.Info($"EntitySystemSingleton Awake: {type.FullName}");
        SystemObject obj = (SystemObject)Activator.CreateInstance(type);

        if (obj is ISystemType iSystemType)
        {
          // 获取或创建一个类型系统，并将系统对象添加到其中
          TypeSystems.OneTypeSystems oneTypeSystems = this.TypeSystems.GetOrCreateOneTypeSystems(iSystemType.Type());
          oneTypeSystems.Map.Add(iSystemType.SystemType(), obj);
          int index = iSystemType.GetInstanceQueueIndex();
          if (index > InstanceQueueIndex.None && index < InstanceQueueIndex.Max)
          {
            oneTypeSystems.QueueFlag[index] = true;
          }
        }
      }
    }

    // 序列化组件的方法
    public void Serialize(Entity component)
    {
      if (component is not ISerialize)
      {
        return;
      }

      // 获取与组件类型相关的序列化系统
      List<SystemObject> iSerializeSystems = this.TypeSystems.GetSystems(component.GetType(), typeof(ISerializeSystem));
      if (iSerializeSystems == null)
      {
        return;
      }

      // 运行每个序列化系统
      foreach (ISerializeSystem serializeSystem in iSerializeSystems)
      {
        if (serializeSystem == null)
        {
          continue;
        }

        try
        {
          serializeSystem.Run(component);
        }
        catch (Exception e)
        {
          ETLog.Error(e);
        }
      }
    }

    // 反序列化组件的方法
    public void Deserialize(Entity component)
    {
      if (component is not IDeserialize)
      {
        return;
      }

      // 获取与组件类型相关的反序列化系统
      List<SystemObject> iDeserializeSystems = this.TypeSystems.GetSystems(component.GetType(), typeof(IDeserializeSystem));
      if (iDeserializeSystems == null)
      {
        return;
      }

      // 运行每个反序列化系统
      foreach (IDeserializeSystem deserializeSystem in iDeserializeSystems)
      {
        if (deserializeSystem == null)
        {
          continue;
        }

        try
        {
          deserializeSystem.Run(component);
        }
        catch (Exception e)
        {
          ETLog.Error(e);
        }
      }
    }

    // 获取组件系统的方法
    public void GetComponentSys(Entity entity, Type type)
    {
      List<SystemObject> iGetSystem = this.TypeSystems.GetSystems(entity.GetType(), typeof(IGetComponentSysSystem));
      if (iGetSystem == null)
      {
        return;
      }

      // 运行每个获取组件系统
      foreach (IGetComponentSysSystem getSystem in iGetSystem)
      {
        if (getSystem == null)
        {
          continue;
        }

        try
        {
          getSystem.Run(entity, type);
        }
        catch (Exception e)
        {
          ETLog.Error(e);
        }
      }
    }

    // 唤醒组件的方法
    public void Awake(Entity component)
    {
      List<SystemObject> iAwakeSystems = this.TypeSystems.GetSystems(component.GetType(), typeof(IAwakeSystem));
      if (iAwakeSystems == null)
      {
        return;
      }

      // 运行每个唤醒系统
      foreach (IAwakeSystem aAwakeSystem in iAwakeSystems)
      {
        if (aAwakeSystem == null)
        {
          continue;
        }

        try
        {
          aAwakeSystem.Run(component);
        }
        catch (Exception e)
        {
          ETLog.Error(e);
        }
      }
    }

    // 带一个参数的唤醒方法
    public void Awake<P1>(Entity component, P1 p1)
    {
      if (component is not IAwake<P1>)
      {
        return;
      }

      List<SystemObject> iAwakeSystems = this.TypeSystems.GetSystems(component.GetType(), typeof(IAwakeSystem<P1>));
      if (iAwakeSystems == null)
      {
        return;
      }

      // 运行每个带参数的唤醒系统
      foreach (IAwakeSystem<P1> aAwakeSystem in iAwakeSystems)
      {
        if (aAwakeSystem == null)
        {
          continue;
        }

        try
        {
          aAwakeSystem.Run(component, p1);
        }
        catch (Exception e)
        {
          ETLog.Error(e);
        }
      }
    }

    // 带两个参数的唤醒方法
    public void Awake<P1, P2>(Entity component, P1 p1, P2 p2)
    {
      if (component is not IAwake<P1, P2>)
      {
        return;
      }

      List<SystemObject> iAwakeSystems = this.TypeSystems.GetSystems(component.GetType(), typeof(IAwakeSystem<P1, P2>));
      if (iAwakeSystems == null)
      {
        return;
      }

      // 运行每个带两个参数的唤醒系统
      foreach (IAwakeSystem<P1, P2> aAwakeSystem in iAwakeSystems)
      {
        if (aAwakeSystem == null)
        {
          continue;
        }

        try
        {
          aAwakeSystem.Run(component, p1, p2);
        }
        catch (Exception e)
        {
          ETLog.Error(e);
        }
      }
    }

    // 带三个参数的唤醒方法
    public void Awake<P1, P2, P3>(Entity component, P1 p1, P2 p2, P3 p3)
    {
      if (component is not IAwake<P1, P2, P3>)
      {
        return;
      }

      List<SystemObject> iAwakeSystems = this.TypeSystems.GetSystems(component.GetType(), typeof(IAwakeSystem<P1, P2, P3>));
      if (iAwakeSystems == null)
      {
        return;
      }

      // 运行每个带三个参数的唤醒系统
      foreach (IAwakeSystem<P1, P2, P3> aAwakeSystem in iAwakeSystems)
      {
        if (aAwakeSystem == null)
        {
          continue;
        }

        try
        {
          aAwakeSystem.Run(component, p1, p2, p3);
        }
        catch (Exception e)
        {
          ETLog.Error(e);
        }
      }
    }

    // 销毁组件的方法
    public void Destroy(Entity component)
    {
      if (component is not IDestroy)
      {
        return;
      }

      List<SystemObject> iDestroySystems = this.TypeSystems.GetSystems(component.GetType(), typeof(IDestroySystem));
      if (iDestroySystems == null)
      {
        return;
      }

      // 运行每个销毁系统
      foreach (IDestroySystem iDestroySystem in iDestroySystems)
      {
        if (iDestroySystem == null)
        {
          continue;
        }

        try
        {
          iDestroySystem.Run(component);
        }
        catch (Exception e)
        {
          ETLog.Error(e);
        }
      }
    }

    // 加载组件的方法，会递归加载子组件和子实体
    public void Load(Entity component)
    {
      // 先处理当前实体
      if (component is ILoad)
      {
        List<SystemObject> iLoadSystems = this.TypeSystems.GetSystems(component.GetType(), typeof(ILoadSystem));
        if (iLoadSystems != null)
        {
          // 运行每个加载系统
          foreach (ILoadSystem loadSystem in iLoadSystems)
          {
            if (loadSystem == null)
            {
              continue;
            }

            try
            {
              loadSystem.Run(component);
            }
            catch (Exception e)
            {
              ETLog.Error(e);
            }
          }
        }
      }

      // 递归处理所有子组件
      if (component.ComponentsCount() > 0)
      {
        foreach (Entity childComponent in component.Components.Values)
        {
          this.Load(childComponent);
        }
      }

      // 递归处理所有子实体
      if (component.ChildrenCount() > 0)
      {
        foreach (Entity childEntity in component.Children.Values)
        {
          this.Load(childEntity);
        }
      }
    }

    // 卸载组件的方法，会递归卸载子实体和子组件
    public void UnLoad(Entity component)
    {
      // 先递归处理所有子实体
      if (component.ChildrenCount() > 0)
      {
        foreach (Entity childEntity in component.Children.Values)
        {
          this.UnLoad(childEntity);
        }
      }

      // 递归处理所有子组件
      if (component.ComponentsCount() > 0)
      {
        foreach (Entity childComponent in component.Components.Values)
        {
          this.UnLoad(childComponent);
        }
      }

      // 最后处理当前实体
      if (component is IUnLoad)
      {
        List<SystemObject> iUnLoadSystems = this.TypeSystems.GetSystems(component.GetType(), typeof(IUnLoadSystem));
        if (iUnLoadSystems != null)
        {
          // 运行每个卸载系统
          foreach (SystemObject systemObject in iUnLoadSystems)
          {
            if (systemObject == null)
            {
              continue;
            }

            try
            {
              IUnLoadSystem unLoadSystem = (IUnLoadSystem)systemObject;
              unLoadSystem.Run(component);
            }
            catch (Exception e)
            {
              ETLog.Error(e);
            }
          }
        }
      }
    }
  }
}