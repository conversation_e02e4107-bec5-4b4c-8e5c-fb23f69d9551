using System;

namespace MaoYouJi
{
  // UnLoad系统，主要在将数据从内存中卸载保存到数据库前调用
  public interface IUnLoad
  {
  }

  public interface IUnLoadSystem : ISystemType
  {
    void Run(Entity o);
  }

  [EntitySystem]
  public abstract class UnLoadSystem<T> : SystemObject, IUnLoadSystem where T : Entity, IUnLoad
  {
    void IUnLoadSystem.Run(Entity o)
    {
      this.UnLoad((T)o);
    }

    Type ISystemType.Type()
    {
      return typeof(T);
    }

    Type ISystemType.SystemType()
    {
      return typeof(IUnLoadSystem);
    }

    int ISystemType.GetInstanceQueueIndex()
    {
      return InstanceQueueIndex.None;
    }

    protected abstract void UnLoad(T self);
  }
}
