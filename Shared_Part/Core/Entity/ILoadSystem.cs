using System;

namespace MaoYouJi
{
  // Load系统，主要在从数据库加载后执行
  public interface ILoad
  {
  }

  public interface ILoadSystem : ISystemType
  {
    void Run(Entity o);
  }

  [EntitySystem]
  public abstract class LoadSystem<T> : SystemObject, ILoadSystem where T : Entity, ILoad
  {
    void ILoadSystem.Run(Entity o)
    {
      this.Load((T)o);
    }

    Type ISystemType.Type()
    {
      return typeof(T);
    }

    Type ISystemType.SystemType()
    {
      return typeof(ILoadSystem);
    }

    int ISystemType.GetInstanceQueueIndex()
    {
      return InstanceQueueIndex.None;
    }

    protected abstract void Load(T self);
  }
}
