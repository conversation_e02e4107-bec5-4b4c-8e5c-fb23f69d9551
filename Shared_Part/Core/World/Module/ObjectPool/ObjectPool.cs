﻿using System;
using System.Collections.Concurrent;
using System.Runtime.CompilerServices;
using System.Threading;

namespace MaoYouJi
{
  /// <summary>
  /// 对象池类 - 用于管理和复用对象实例
  /// 实现单例模式，并支持自动初始化
  /// </summary>
  public class ObjectPool : Singleton<ObjectPool>, ISingletonAwake
  {
    // 存储不同类型对象池的并发字典
    private ConcurrentDictionary<Type, Pool> objPool;

    // 创建新对象池的委托函数，默认容量为1000
    private readonly Func<Type, Pool> AddPoolFunc = type => new Pool(type, 1000);

    // 初始化对象池
    public void Awake()
    {
      lock (this)
      {
        objPool = new ConcurrentDictionary<Type, Pool>();
      }
    }

    /// <summary>
    /// 泛型方法：获取指定类型的对象
    /// </summary>
    public T Fetch<T>() where T : class
    {
      return this.Fetch(typeof(T)) as T;
    }

    /// <summary>
    /// 从对象池中获取指定类型的对象
    /// </summary>
    /// <param name="type">对象类型</param>
    /// <param name="isFromPool">是否从对象池中获取，false则直接新建</param>
    public object Fetch(Type type, bool isFromPool = false)
    {
      if (!isFromPool)
      {
        return Activator.CreateInstance(type);
      }

      Pool pool = GetPool(type);
      object obj = pool.Get();
      // 如果对象实现了IPool接口，标记其来源于对象池
      if (obj is IPool p)
      {
        p.IsFromPool = true;
      }
      return obj;
    }

    /// <summary>
    /// 回收对象到对象池
    /// </summary>
    public void Recycle(object obj)
    {
      if (obj is IPool p)
      {
        // 如果对象不是来自对象池，直接返回
        if (!p.IsFromPool)
        {
          return;
        }

        // 防止多次入池，将标记设为false
        p.IsFromPool = false;
      }

      Type type = obj.GetType();
      Pool pool = GetPool(type);
      pool.Return(obj);
    }

    /// <summary>
    /// 获取指定类型的对象池，如果不存在则创建新的
    /// </summary>
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    private Pool GetPool(Type type)
    {
      return this.objPool.GetOrAdd(type, AddPoolFunc);
    }

    /// <summary>
    /// 内部Pool类 - 实现线程安全的无锁对象池
    /// </summary>
    private class Pool
    {
      private readonly Type ObjectType;      // 对象类型
      private readonly int MaxCapacity;      // 对象池最大容量
      private int NumItems;                  // 当前对象数量
      private readonly ConcurrentQueue<object> _items = new(); // 对象队列
      private object FastItem;               // 快速访问项，用于优化性能

      public Pool(Type objectType, int maxCapacity)
      {
        ObjectType = objectType;
        MaxCapacity = maxCapacity;
      }

      /// <summary>
      /// 获取一个对象实例
      /// 1. 优先尝试获取FastItem
      /// 2. 其次从队列中获取
      /// 3. 最后创建新实例
      /// </summary>
      public object Get()
      {
        object item = FastItem;
        // 尝试获取并清空FastItem
        if (item == null || Interlocked.CompareExchange(ref FastItem, null, item) != item)
        {
          // 尝试从队列中获取对象
          if (_items.TryDequeue(out item))
          {
            Interlocked.Decrement(ref NumItems);
            return item;
          }

          // 如果队列为空，创建新实例
          return Activator.CreateInstance(this.ObjectType);
        }

        return item;
      }

      /// <summary>
      /// 归还对象到池中
      /// 1. 优先尝试设置为FastItem
      /// 2. 如果FastItem已占用，则加入队列
      /// 3. 如果超出容量限制，则丢弃
      /// </summary>
      public void Return(object obj)
      {
        // 尝试设置为FastItem
        if (FastItem != null || Interlocked.CompareExchange(ref FastItem, obj, null) != null)
        {
          // FastItem已被占用，尝试加入队列
          if (Interlocked.Increment(ref NumItems) <= MaxCapacity)
          {
            _items.Enqueue(obj);
            return;
          }

          // 超出容量限制，递减计数
          Interlocked.Decrement(ref NumItems);
        }
      }
    }
  }
}