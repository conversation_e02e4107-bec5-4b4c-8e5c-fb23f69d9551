﻿using System.Collections.Generic;
using System.Reflection;
using System;

namespace MaoYouJi
{
  public class CodeTypes : Singleton<CodeTypes>, ISingletonAwake<Assembly[]>
  {
    private readonly Dictionary<string, Type> allTypes = new();
    private readonly UnOrderMultiMapSet<Type, Type> types = new();

    public void Awake(Assembly[] assemblies)
    {
      Dictionary<string, Type> addTypes = AssemblyHelper.GetAssemblyTypes(assemblies);
      foreach ((string fullName, Type type) in addTypes)
      {
        this.allTypes[fullName] = type;

        if (type.IsAbstract)
        {
          continue;
        }

        // 记录所有的有BaseAttribute标记的的类型
        object[] objects = type.GetCustomAttributes(typeof(BaseAttribute), true);

        foreach (object o in objects)
        {
          this.types.Add(o.GetType(), type);
        }
      }
    }

    public HashSet<Type> GetTypes(Type systemAttributeType)
    {
      if (!this.types.ContainsKey(systemAttributeType))
      {
        return new HashSet<Type>();
      }

      return this.types[systemAttributeType];
    }

    public Dictionary<string, Type> GetTypes()
    {
      return allTypes;
    }

    public Type GetType(string typeName)
    {
      return this.allTypes[typeName];
    }

    public void CreateCode()
    {
      var hashSet = this.GetTypes(typeof(CodeAttribute));
      foreach (Type type in hashSet)
      {
        ETLog.Info($"CreateCode: {type.FullName}");
        object obj = Activator.CreateInstance(type);
        ((ISingletonAwake)obj).Awake();
        World.Instance.AddSingleton((ASingleton)obj);
      }
    }
  }
}