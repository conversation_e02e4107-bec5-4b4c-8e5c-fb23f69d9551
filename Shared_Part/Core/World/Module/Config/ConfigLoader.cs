﻿using System;
using System.Collections.Generic;
#if DOTNET || UNITY_STANDALONE
using System.Threading.Tasks;
#endif

namespace MaoYouJi
{
  /// <summary>
  /// ConfigLoader会扫描所有的有ConfigAttribute标签的配置,加载进来
  /// </summary>
  public class ConfigLoader : Singleton<ConfigLoader>, ISingletonAwake
  {
    public struct GetAllConfigBytes
    {
    }

    public struct GetOneConfigBytes
    {
      public string ConfigName;
    }

    public struct GetAllConfigJson
    {
    }

    public void Awake()
    {
    }

    public async ETTask Reload(Type configType)
    {
      GetOneConfigBytes getOneConfigBytes = new() { ConfigName = configType.Name };
      byte[] oneConfigBytes = await EventSystem.Instance.Invoke<GetOneConfigBytes, ETTask<byte[]>>(getOneConfigBytes);
      LoadOneConfig(configType, oneConfigBytes);
    }

    public async ETTask LoadAsync()
    {
      Dictionary<Type, string> configJson = await EventSystem.Instance.Invoke<GetAllConfigJson, ETTask<Dictionary<Type, string>>>(new GetAllConfigJson());

#if DOTNET || UNITY_STANDALONE
      using ListComponent<Task> listTasks = ListComponent<Task>.Create();

      foreach (Type type in configJson.Keys)
      {
        string oneConfigJson = configJson[type];
        Task task = Task.Run(() => LoadOneConfig(type, oneConfigJson));
        listTasks.Add(task);
      }

      await Task.WhenAll(listTasks.ToArray());
#else
            // foreach (Type type in configBytes.Keys)
            // {
            //     LoadOneConfig(type, configBytes[type]);
            // }
#endif
    }

    private static void LoadOneConfig(Type configType, byte[] oneConfigBytes)
    {
      object category = MongoHelper.Deserialize(configType, oneConfigBytes, 0, oneConfigBytes.Length);
      ASingleton singleton = category as ASingleton;
      World.Instance.AddSingleton(singleton);
    }

    private static void LoadOneConfig(Type configType, string oneConfigJson)
    {
      object category = MongoHelper.FromJson(configType, oneConfigJson);
      ASingleton singleton = category as ASingleton;
      World.Instance.AddSingleton(singleton);
    }
  }
}