﻿using System;
using System.Collections.Generic;

namespace MaoYouJi
{
  [Code]
  public class EventSystem : Singleton<EventSystem>, ISingletonAwake
  {
    // 内部类，用于存储事件信息，包括事件接口和场景类型
    private class EventInfo
    {
      public IEvent IEvent { get; }
      public SceneType SceneType { get; }

      public EventInfo(IEvent iEvent, SceneType sceneType)
      {
        this.IEvent = iEvent;
        this.SceneType = sceneType;
      }
    }

    // 存储所有事件的字典，键为事件类型，值为事件信息列表
    private readonly Dictionary<Type, List<EventInfo>> allEvents = new();

    // 存储所有调用者的字典，键为类型，值为另一个字典（键为长整型，值为对象）
    private readonly Dictionary<Type, Dictionary<long, object>> allInvokers = new();

    // 初始化方法，加载所有事件和调用者
    public void Awake()
    {
      CodeTypes codeTypes = CodeTypes.Instance;
      foreach (Type type in codeTypes.GetTypes(typeof(EventAttribute)))
      {
        IEvent obj = Activator.CreateInstance(type) as IEvent;
        if (obj == null)
        {
          throw new Exception($"type not is AEvent: {type.Name}");
        }

        object[] attrs = type.GetCustomAttributes(typeof(EventAttribute), false);
        foreach (object attr in attrs)
        {
          EventAttribute eventAttribute = attr as EventAttribute;
          Type eventType = obj.Type;
          EventInfo eventInfo = new(obj, eventAttribute.SceneType);

          if (!this.allEvents.ContainsKey(eventType))
          {
            this.allEvents.Add(eventType, new List<EventInfo>());
          }
          this.allEvents[eventType].Add(eventInfo);
        }
      }

      foreach (Type type in codeTypes.GetTypes(typeof(InvokeAttribute)))
      {
        object obj = Activator.CreateInstance(type);
        IInvoke iInvoke = obj as IInvoke;
        if (iInvoke == null)
        {
          throw new Exception($"type not is callback: {type.Name}");
        }

        object[] attrs = type.GetCustomAttributes(typeof(InvokeAttribute), false);
        foreach (object attr in attrs)
        {
          if (!this.allInvokers.TryGetValue(iInvoke.Type, out var dict))
          {
            dict = new Dictionary<long, object>();
            this.allInvokers.Add(iInvoke.Type, dict);
          }

          InvokeAttribute invokeAttribute = attr as InvokeAttribute;

          try
          {
            dict.Add(invokeAttribute.Type, obj);
          }
          catch (Exception e)
          {
            throw new Exception($"action type duplicate: {iInvoke.Type.Name} {invokeAttribute.Type}", e);
          }
        }
      }
    }

    // 异步发布事件的方法，发布给所有符合条件的事件处理器
    public async ETTask PublishAsync<S, T>(S scene, T a) where S : class, IScene where T : struct
    {
      List<EventInfo> iEvents;
      if (!this.allEvents.TryGetValue(typeof(T), out iEvents))
      {
        return;
      }

      using ListComponent<ETTask> list = ListComponent<ETTask>.Create();

      foreach (EventInfo eventInfo in iEvents)
      {
        if (!scene.SceneType.HasSameFlag(eventInfo.SceneType))
        {
          continue;
        }

        if (!(eventInfo.IEvent is AEvent<S, T> aEvent))
        {
          ETLog.Error($"event error: {eventInfo.IEvent.GetType().FullName}");
          continue;
        }

        list.Add(aEvent.Handle(scene, a));
      }

      try
      {
        await ETTaskHelper.WaitAll(list);
      }
      catch (Exception e)
      {
        ETLog.Error(e);
      }
    }

    // 同步发布事件的方法
    public void Publish<S, T>(S scene, T a) where S : class, IScene where T : struct
    {
      List<EventInfo> iEvents;
      if (!this.allEvents.TryGetValue(typeof(T), out iEvents))
      {
        return;
      }

      SceneType sceneType = scene.SceneType;
      foreach (EventInfo eventInfo in iEvents)
      {
        if (!sceneType.HasSameFlag(eventInfo.SceneType))
        {
          continue;
        }

        if (!(eventInfo.IEvent is AEvent<S, T> aEvent))
        {
          ETLog.Error($"event error: {eventInfo.IEvent.GetType().FullName}");
          continue;
        }

        aEvent.Handle(scene, a).Coroutine();
      }
    }

    // Invoke跟Publish的区别(特别注意)
    // Invoke类似函数，必须有被调用方，否则异常，调用者跟被调用者属于同一模块，比如MoveComponent中的Timer计时器，调用跟被调用的代码均属于移动模块
    // 既然Invoke跟函数一样，那么为什么不使用函数呢? 因为有时候不方便直接调用，比如Config加载，在客户端跟服务端加载方式不一样。比如TimerComponent需要根据Id分发
    // 注意，不要把Invoke当函数使用，这样会造成代码可读性降低，能用函数不要用Invoke
    // publish是事件，抛出去可以没人订阅，调用者跟被调用者属于两个模块，比如任务系统需要知道道具使用的信息，则订阅道具使用事件
    // Invoke方法，用于调用特定类型的处理器
    public void Invoke<A>(long type, A args) where A : struct
    {
      if (!this.allInvokers.TryGetValue(typeof(A), out var invokeHandlers))
      {
        throw new Exception($"Invoke error1: {type} {typeof(A).FullName}");
      }
      if (!invokeHandlers.TryGetValue(type, out var invokeHandler))
      {
        throw new Exception($"Invoke error2: {type} {typeof(A).FullName}");
      }

      var aInvokeHandler = invokeHandler as AInvokeHandler<A>;
      if (aInvokeHandler == null)
      {
        throw new Exception($"Invoke error3, not AInvokeHandler: {type} {typeof(A).FullName}");
      }

      aInvokeHandler.Handle(args);
    }

    public bool TryGetInvoke<A>(long type, out AInvokeHandler<A> aInvoke) where A : struct
    {
      if (!this.allInvokers.TryGetValue(typeof(A), out var invokeHandlers))
      {
        aInvoke = null;
        return false;
      }
      if (!invokeHandlers.TryGetValue(type, out var invokeHandler))
      {
        aInvoke = null;
        return false;
      }
      aInvoke = invokeHandler as AInvokeHandler<A>;
      return true;
    }

    // 泛型Invoke方法，返回值类型为T
    public T Invoke<A, T>(long type, A args) where A : struct
    {
      if (!this.allInvokers.TryGetValue(typeof(A), out var invokeHandlers))
      {
        ETLog.Info("type of" + typeof(A).FullName);
        throw new Exception($"Invoke error4: {type} {typeof(A).FullName}");
      }

      if (!invokeHandlers.TryGetValue(type, out var invokeHandler))
      {
        throw new Exception($"Invoke error5: {type} {typeof(A).FullName}");
      }

      var aInvokeHandler = invokeHandler as AInvokeHandler<A, T>;
      if (aInvokeHandler == null)
      {
        throw new Exception($"Invoke error6, not AInvokeHandler: {type} {typeof(A).FullName} {typeof(T).FullName} ");
      }

      return aInvokeHandler.Handle(args);
    }

    public bool TryGetInvoke<A, T>(long type, out AInvokeHandler<A, T> aInvoke) where A : struct
    {
      if (!this.allInvokers.TryGetValue(typeof(A), out var invokeHandlers))
      {
        aInvoke = null;
        return false;
      }
      if (!invokeHandlers.TryGetValue(type, out var invokeHandler))
      {
        aInvoke = null;
        return false;
      }
      aInvoke = invokeHandler as AInvokeHandler<A, T>;
      return true;
    }

    // 重载的Invoke方法，默认type为0
    public void Invoke<A>(A args) where A : struct
    {
      Invoke(0, args);
    }

    // 重载的泛型Invoke方法，默认type为0
    public T Invoke<A, T>(A args) where A : struct
    {
      return Invoke<A, T>(0, args);
    }
  }
}
