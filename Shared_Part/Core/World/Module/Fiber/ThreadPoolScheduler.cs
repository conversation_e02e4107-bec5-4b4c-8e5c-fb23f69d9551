﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;

namespace MaoYouJi
{
  internal class ThreadPoolScheduler : ETIScheduler
  {
    // 线程列表，用于存储所有的线程
    private readonly List<Thread> threads;

    // 线程安全的队列，用于存储纤程ID
    private readonly ConcurrentQueue<int> idQueue = new();

    // 纤程管理器，用于管理纤程
    private readonly FiberManager fiberManager;

    // 构造函数，初始化线程池调度器
    public ThreadPoolScheduler(FiberManager fiberManager)
    {
      this.fiberManager = fiberManager;
      int threadCount = Environment.ProcessorCount; // 获取处理器核心数
      this.threads = new List<Thread>(threadCount);
      for (int i = 0; i < threadCount; ++i)
      {
        Thread thread = new(this.Loop); // 创建新线程并指定执行方法
        this.threads.Add(thread);
        thread.Start(); // 启动线程
      }
    }

    // 线程执行的循环方法
    private void Loop()
    {
      int count = 0;
      while (true)
      {
        if (count <= 0)
        {
          Thread.Sleep(1); // 休眠1毫秒

          // 计算每个线程需要处理的纤程数量，最小为1
          count = this.fiberManager.Count() / this.threads.Count + 1;
        }

        --count;

        if (this.fiberManager.IsDisposed())
        {
          return; // 如果纤程管理器已被释放，退出循环
        }

        if (!this.idQueue.TryDequeue(out int id))
        {
          Thread.Sleep(1); // 如果队列为空，休眠1毫秒
          continue;
        }

        Fiber fiber = this.fiberManager.Get(id); // 获取纤程
        if (fiber == null)
        {
          continue; // 如果纤程不存在，继续下一个循环
        }

        if (fiber.IsDisposed)
        {
          continue; // 如果纤程已被释放，继续下一个循环
        }

        Fiber.Instance = fiber; // 设置当前纤程实例
        SynchronizationContext.SetSynchronizationContext(fiber.ThreadSynchronizationContext); // 设置同步上下文
        fiber.Update(); // 更新纤程
        fiber.LateUpdate(); // 延迟更新纤程
        SynchronizationContext.SetSynchronizationContext(null); // 清除同步上下文
        Fiber.Instance = null; // 清除当前纤程实例

        this.idQueue.Enqueue(id); // 将纤程ID重新加入队列
      }
    }

    // 释放资源，等待所有线程结束
    public void Dispose()
    {
      foreach (Thread thread in this.threads)
      {
        thread.Join(); // 等待线程结束
      }
    }

    // 添加纤程ID到队列
    public void Add(int fiberId)
    {
      this.idQueue.Enqueue(fiberId);
    }
  }
}