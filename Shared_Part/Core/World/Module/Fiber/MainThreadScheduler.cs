﻿using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;

namespace MaoYouJi
{
  // MainThreadScheduler类实现了IScheduler接口，用于管理和调度Fiber实例
  internal class MainThreadScheduler : ETIScheduler
  {
    // 用于存储Fiber ID的队列
    private readonly ConcurrentQueue<int> idQueue = new();
    // 用于存储待添加的Fiber ID的队列
    private readonly ConcurrentQueue<int> addIds = new();
    // Fiber管理器，用于获取Fiber实例
    private readonly FiberManager fiberManager;
    // 线程同步上下文，用于管理线程间的同步
    private readonly ThreadSynchronizationContext threadSynchronizationContext = new();

    // 构造函数，初始化Fiber管理器和同步上下文
    public MainThreadScheduler(FiberManager fiberManager)
    {
      // 设置当前线程的同步上下文
      SynchronizationContext.SetSynchronizationContext(this.threadSynchronizationContext);
      this.fiberManager = fiberManager;
    }

    // 释放资源，清空队列
    public void Dispose()
    {
      this.addIds.Clear();
      this.idQueue.Clear();
    }

    // 更新方法，调度和更新Fiber实例
    public void Update()
    {
      // 设置当前线程的同步上下文
      SynchronizationContext.SetSynchronizationContext(this.threadSynchronizationContext);
      this.threadSynchronizationContext.Update();

      int count = this.idQueue.Count;
      while (count-- > 0)
      {
        if (!this.idQueue.TryDequeue(out int id))
        {
          continue;
        }

        // 获取Fiber实例
        Fiber fiber = this.fiberManager.Get(id);
        if (fiber == null || fiber.IsDisposed)
        {
          continue;
        }

        // 设置当前线程的Fiber实例
        Fiber.Instance = fiber;
        SynchronizationContext.SetSynchronizationContext(fiber.ThreadSynchronizationContext);
        fiber.Update();
        Fiber.Instance = null;

        // 将Fiber ID重新加入队列
        this.idQueue.Enqueue(id);
      }

      // 还原默认的同步上下文
      SynchronizationContext.SetSynchronizationContext(this.threadSynchronizationContext);
    }

    // LateUpdate方法，处理延迟更新
    public void LateUpdate()
    {
      int count = this.idQueue.Count;
      while (count-- > 0)
      {
        if (!this.idQueue.TryDequeue(out int id))
        {
          continue;
        }

        // 获取Fiber实例
        Fiber fiber = this.fiberManager.Get(id);
        if (fiber == null || fiber.IsDisposed)
        {
          continue;
        }

        // 设置当前线程的Fiber实例
        Fiber.Instance = fiber;
        SynchronizationContext.SetSynchronizationContext(fiber.ThreadSynchronizationContext);
        fiber.LateUpdate();
        Fiber.Instance = null;

        // 将Fiber ID重新加入队列
        this.idQueue.Enqueue(id);
      }

      // 将待添加的Fiber ID加入队列
      while (this.addIds.Count > 0)
      {
        this.addIds.TryDequeue(out int result);
        this.idQueue.Enqueue(result);
      }

      // 还原默认的同步上下文
      SynchronizationContext.SetSynchronizationContext(this.threadSynchronizationContext);
    }

    // 添加Fiber ID到待添加队列
    public void Add(int fiberId = 0)
    {
      this.addIds.Enqueue(fiberId);
    }
  }
}