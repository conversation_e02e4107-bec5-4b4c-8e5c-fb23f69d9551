﻿using System;
using System.Runtime.InteropServices;
using System.Threading;

namespace MaoYouJi
{
  [StructLayout(LayoutKind.Sequential, Pack = 1)]
  public struct IdStruct
  {
    public short Process;  // 14bit
    public uint Time;    // 30bit
    public uint Value;   // 20bit

    public long ToLong()
    {
      ulong result = 0;
      result |= (ushort)this.Process;
      result <<= 30;
      result |= this.Time;
      result <<= 20;
      result |= this.Value;
      return (long)result;
    }

    public IdStruct(uint time, short process, uint value)
    {
      this.Process = process;
      this.Time = time;
      this.Value = value;
    }

    public IdStruct(long id)
    {
      ulong result = (ulong)id;
      this.Value = (uint)(result & IdGenerater.Mask20bit);
      result >>= 20;
      this.Time = (uint)result & IdGenerater.Mask30bit;
      result >>= 30;
      this.Process = (short)(result & IdGenerater.Mask14bit);
    }

    public override string ToString()
    {
      return $"process: {this.Process}, time: {this.Time}, value: {this.Value}";
    }
  }

  [StructLayout(LayoutKind.Sequential, Pack = 1)]
  public struct InstanceIdStruct
  {
    public uint Time;  // 32bit
    public uint Value; // 32bit

    public long ToLong()
    {
      ulong result = 0;
      result |= this.Time;
      result <<= 32;
      result |= this.Value;
      return (long)result;
    }

    public InstanceIdStruct(uint time, uint value)
    {
      this.Time = time;
      this.Value = value;
    }

    public InstanceIdStruct(long id)
    {
      ulong result = (ulong)id;
      this.Value = (uint)(result & uint.MaxValue);
      result >>= 32;
      this.Time = (uint)(result & uint.MaxValue);
    }

    public override string ToString()
    {
      return $"time: {this.Time}, value: {this.Value}";
    }
  }

  public class IdGenerater : Singleton<IdGenerater>, ISingletonAwake
  {
    public const int MaxZone = 1024;

    public const int Mask14bit = 0x3fff;
    public const int Mask30bit = 0x3fffffff;
    public const int Mask20bit = 0xfffff;

    private long epoch2022;

    private int value;
    private int instanceIdValue;
    private int requestIdCounter;
    private readonly Random random = new Random();

    public void Awake()
    {
      long epoch1970tick = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc).Ticks / 10000;
      this.epoch2022 = new DateTime(2022, 1, 1, 0, 0, 0, DateTimeKind.Utc).Ticks / 10000 - epoch1970tick;
    }

    private uint TimeSince2022()
    {
      uint a = (uint)((TimeInfo.Instance.FrameTime - this.epoch2022) / 1000);
      return a;
    }

    public long GenerateId()
    {
      uint time = TimeSince2022();
      int v = 0;
      // 这里必须加锁
      lock (this)
      {
        if (++this.value > Mask20bit - 1)
        {
          this.value = 0;
        }
        v = this.value;
      }

      IdStruct idStruct = new(time, (short)Options.Instance.Process, (uint)v);
      return idStruct.ToLong();
    }

    public long GenerateInstanceId()
    {
      uint time = this.TimeSince2022();
      uint v = (uint)Interlocked.Add(ref this.instanceIdValue, 1);
      InstanceIdStruct instanceIdStruct = new(time, v);
      return instanceIdStruct.ToLong();
    }

    /// <summary>
    /// 生成32位的requestId，不上锁支持高并发
    /// 可能有极小概率重复，适用于请求ID场景
    /// </summary>
    /// <returns>32位无符号整型requestId</returns>
    public int GenerateRequestId()
    {
      // 获取当前时间戳（秒级）- 占用高16位
      uint timestamp = (uint)(DateTimeOffset.UtcNow.ToUnixTimeSeconds() & 0xFFFF);

      // 使用进程ID - 占用4位
      uint processId = (uint)(Options.Instance.Process & 0xF);

      // 获取自增计数 - 占用低12位
      uint counter = (uint)(Interlocked.Increment(ref this.requestIdCounter) & 0xFFF);

      // 组合成32位ID: 16位时间戳 + 4位进程ID + 12位计数器
      return Math.Abs((int)((timestamp << 16) | (processId << 12) | counter));
    }
  }
}