﻿using System;
using System.Collections.Generic;
using NLog;

namespace MaoYouJi
{
  public class NLogger : ILog
  {
    private readonly NLog.Logger logger;

    // 进程级别的日志缓存
    [StaticField]
    private static readonly Dictionary<int, NLog.Logger> processLoggers = new Dictionary<int, NLog.Logger>();

    static NLogger()
    {
      LogManager.Configuration = new NLog.Config.XmlLoggingConfiguration("../Config/NLog/NLog.config");
      LogManager.Configuration.Variables["currentDir"] = Environment.CurrentDirectory;
    }

    public NLogger(string name, int process, int fiber)
    {
      // 使用进程ID作为主要标识，将所有Fiber的日志合并到同一个日志文件
      if (!processLoggers.TryGetValue(process, out this.logger))
      {
        this.logger = LogManager.GetLogger($"{(uint)process:000000}.{name}");
        processLoggers[process] = this.logger;
      }
    }

    public void Trace(string message)
    {
      this.logger.Trace(message);
    }

    public void Warning(string message)
    {
      this.logger.Warn(message);
    }

    public void Info(string message)
    {
      this.logger.Info(message);
    }

    public void Debug(string message)
    {
      this.logger.Debug(message);
    }

    public void Error(string message)
    {
      this.logger.Error(message);
    }

    public void Error(Exception e)
    {
      this.logger.Error(e.ToString());
    }

#if DOTNET
    public void Trace(ref System.Runtime.CompilerServices.DefaultInterpolatedStringHandler message)
    {
      this.logger.Trace(message.ToStringAndClear());
    }

    public void Warning(ref System.Runtime.CompilerServices.DefaultInterpolatedStringHandler message)
    {
      this.logger.Warn(message.ToStringAndClear());
    }

    public void Info(ref System.Runtime.CompilerServices.DefaultInterpolatedStringHandler message)
    {
      this.logger.Info(message.ToStringAndClear());
    }

    public void Debug(ref System.Runtime.CompilerServices.DefaultInterpolatedStringHandler message)
    {
      this.logger.Debug(message.ToStringAndClear());
    }

    public void Error(ref System.Runtime.CompilerServices.DefaultInterpolatedStringHandler message)
    {
      this.logger.Error(message.ToStringAndClear());
    }
#endif
  }
}
