using System;
using System.Diagnostics;

namespace MaoYouJi
{
  public static class ETLog
  {
    private const int TraceLevel = 1;
    private const int DebugLevel = 2;
    private const int InfoLevel = 3;
    private const int WarningLevel = 4;

    private static ILog GetLog()
    {
      return Fiber.Instance != null ? Fiber.Instance.Log : Logger.Instance.Log;
    }

    private static string GetMsg(string msg)
    {
      string content = $"[f-{Fiber.Instance?.Id ?? 0}]";
      if (MaoAsyncLocal.RequestId != 0)
      {
        // 添加Fiber ID信息
        content += $"[r-{MaoAsyncLocal.RequestId}]";
      }
      if (MaoAsyncLocal.AttackId != null)
      {
        content += $"[a-{MaoAsyncLocal.AttackId}]";
      }
      return $"{content} {msg}";
    }

    [Conditional("DEBUG")]
    public static void Debug(string msg)
    {
      if (Options.Instance.LogLevel > DebugLevel)
      {
        return;
      }

      GetLog().Debug(GetMsg(msg));
    }

    [Conditional("DEBUG")]
    public static void Trace(string msg)
    {
      if (Options.Instance.LogLevel > TraceLevel)
      {
        return;
      }
      StackTrace st = new(1, true);
      msg = GetMsg(msg);
      GetLog().Trace($"{msg}\n{st}");
    }

    public static void Info(string msg)
    {
      if (Options.Instance.LogLevel > InfoLevel)
      {
        return;
      }
      GetLog().Info(GetMsg(msg));
    }

    public static void TraceInfo(string msg)
    {
      if (Options.Instance.LogLevel > InfoLevel)
      {
        return;
      }
      StackTrace st = new(1, true);
      msg = GetMsg(msg);
      GetLog().Trace($"{msg}\n{st}");
    }

    public static void Warning(string msg)
    {
      if (Options.Instance.LogLevel > WarningLevel)
      {
        return;
      }
      GetLog().Warning(GetMsg(msg));
    }

    public static void Error(string msg)
    {
      StackTrace st = new(1, true);
      msg = GetMsg(msg);
      GetLog().Error($"{msg}\n{st}");
    }

    public static void Error(Exception e)
    {
      GetLog().Error(e.ToString());
    }

    public static void Console(string msg)
    {
      if (Options.Instance.Console == 1)
      {
        System.Console.WriteLine(msg);
      }
      GetLog().Debug(GetMsg(msg));
    }

#if DOTNET
    private static string GetMsg(ref System.Runtime.CompilerServices.DefaultInterpolatedStringHandler message)
    {
      return GetMsg(message.ToStringAndClear());
    }

    [Conditional("DEBUG")]
    public static void Trace(ref System.Runtime.CompilerServices.DefaultInterpolatedStringHandler message)
    {
      if (Options.Instance.LogLevel > TraceLevel)
      {
        return;
      }
      StackTrace st = new(1, true);
      GetLog().Trace($"{GetMsg(ref message)}\n{st.ToString()}");
    }
    [Conditional("DEBUG")]
    public static void Warning(ref System.Runtime.CompilerServices.DefaultInterpolatedStringHandler message)
    {
      if (Options.Instance.LogLevel > WarningLevel)
      {
        return;
      }
      GetLog().Warning(GetMsg(ref message));
    }

    public static void Info(ref System.Runtime.CompilerServices.DefaultInterpolatedStringHandler message)
    {
      if (Options.Instance.LogLevel > InfoLevel)
      {
        return;
      }
      GetLog().Info(GetMsg(ref message));
    }
    [Conditional("DEBUG")]
    public static void Debug(ref System.Runtime.CompilerServices.DefaultInterpolatedStringHandler message)
    {
      if (Options.Instance.LogLevel > DebugLevel)
      {
        return;
      }
      GetLog().Debug(GetMsg(ref message));
    }

    public static void Error(ref System.Runtime.CompilerServices.DefaultInterpolatedStringHandler message)
    {
      GetLog().Error(GetMsg(ref message));
    }
#endif
  }
}
