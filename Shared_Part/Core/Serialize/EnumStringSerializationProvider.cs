using System;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Serializers;

namespace MaoYouJi
{
  /// <summary>
  /// 枚举字符串序列化提供者，确保所有枚举都以字符串形式存储在MongoDB中
  /// </summary>
  public class EnumStringSerializationProvider : IBsonSerializationProvider
  {
    public IBsonSerializer GetSerializer(Type type)
    {
      // 检查类型是否为枚举
      if (type != null && type.IsEnum)
      {
        // 创建一个泛型枚举序列化器，指定以字符串形式表示
        Type serializerType = typeof(EnumSerializer<>).MakeGenericType(type);
        return (IBsonSerializer)Activator.CreateInstance(serializerType, BsonType.String);
      }

      return null;
    }
  }
}
