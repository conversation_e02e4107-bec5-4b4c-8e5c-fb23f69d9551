using System;
using System.Runtime.CompilerServices;
using NativeCollection.UnsafeType;

namespace NativeCollection
{
  // 定义一个非托管的泛型类 NativePool，要求类型参数 T 必须是非托管类型，并实现 IEquatable<T> 和 IPool 接口
  public unsafe class NativePool<T> : INativeCollectionClass where T : unmanaged, IEquatable<T>, IPool
  {
    // 指向非托管堆栈池的指针
    private UnsafeType.NativeStackPool<T>* _nativePool;
    // 默认池大小常量
    private const int _defaultPoolSize = 200;
    // 实际池大小
    private int _poolSize;

    // 构造函数，初始化池大小并创建非托管堆栈池
    public NativePool(int maxPoolSize = _defaultPoolSize)
    {
      _poolSize = maxPoolSize;
      _nativePool = UnsafeType.NativeStackPool<T>.Create(_poolSize);
      IsDisposed = false;
    }

    // 分配一个对象
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public T* Alloc()
    {
      return _nativePool->Alloc();
    }

    // 归还一个对象
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public void Return(T* ptr)
    {
      _nativePool->Return(ptr);
    }

    // 释放资源
    public void Dispose()
    {
      if (IsDisposed)
      {
        return;
      }
      if (_nativePool != null)
      {
        _nativePool->Dispose();
        NativeMemoryHelper.Free(_nativePool);
        NativeMemoryHelper.RemoveNativeMemoryByte(Unsafe.SizeOf<UnsafeType.NativeStackPool<T>>());
        IsDisposed = true;
      }
    }

    // 重新初始化池
    public void ReInit()
    {
      if (IsDisposed)
      {
        _nativePool = UnsafeType.NativeStackPool<T>.Create(_poolSize);
        IsDisposed = false;
      }
    }

    // 表示池是否已被释放
    public bool IsDisposed { get; private set; }
  }
}

