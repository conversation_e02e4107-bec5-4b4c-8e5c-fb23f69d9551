/*
Copyright (c) 2009-2010 <PERSON><PERSON><PERSON> <EMAIL>
recast4j copyright (c) 2015-2019 <PERSON><PERSON>r <PERSON><EMAIL>
DotRecast Copyright (c) 2023 Choi <PERSON>l <EMAIL>

This software is provided 'as-is', without any express or implied
warranty.  In no event will the authors be held liable for any damages
arising from the use of this software.
Permission is granted to anyone to use this software for any purpose,
including commercial applications, and to alter it and redistribute it
freely, subject to the following restrictions:
1. The origin of this software must not be misrepresented; you must not
 claim that you wrote the original software. If you use this software
 in a product, an acknowledgment in the product documentation would be
 appreciated but is not required.
2. Altered source versions must be plainly marked as such, and must not be
 misrepresented as being the original software.
3. This notice may not be removed or altered from any source distribution.
*/

using DotRecast.Core;

namespace DotRecast.Detour
{
    public class DtNodeQueue
    {
        private readonly RcSortedQueue<DtNode> m_heap = new RcSortedQueue<DtNode>((n1, n2) => n1.total.CompareTo(n2.total));

        public int Count()
        {
            return m_heap.Count();
        }

        public void Clear()
        {
            m_heap.Clear();
        }

        public DtNode Peek()
        {
            return m_heap.Peek();
        }

        public DtNode Pop()
        {
            var node = Peek();
            m_heap.Remove(node);
            return node;
        }

        public void Push(DtNode node)
        {
            m_heap.Enqueue(node);
        }

        public void Modify(DtNode node)
        {
            m_heap.Remove(node);
            Push(node);
        }

        public bool IsEmpty()
        {
            return 0 == m_heap.Count();
        }
    }
}