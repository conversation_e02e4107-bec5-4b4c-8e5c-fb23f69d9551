﻿using System;
using System.Collections.Generic;

namespace MaoYouJi
{
  public static class ETTaskHelper
  {
    // 检查ETCancellationToken是否被取消
    public static bool IsCancel(this ETCancellationToken self)
    {
      if (self == null)
      {
        return false; // 如果self为null，返回false
      }
      return self.IsDispose(); // 调用IsDispose方法检查是否被取消
    }

    // 内部类，用于管理协程的阻塞
    private class CoroutineBlocker
    {
      private int count; // 计数器，用于跟踪未完成的任务数
      private ETTask tcs; // 用于等待所有子协程完成的任务

      // 构造函数，初始化计数器
      public CoroutineBlocker(int count)
      {
        this.count = count;
      }

      // 异步运行子协程
      public async ETTask RunSubCoroutineAsync(ETTask task)
      {
        try
        {
          await task; // 等待子任务完成
        }
        finally
        {
          --this.count; // 任务完成后，减少计数器

          // 如果所有任务都完成，设置tcs的结果
          if (this.count <= 0 && this.tcs != null)
          {
            ETTask t = this.tcs;
            this.tcs = null;
            t.SetResult();
          }
        }
      }

      // 等待所有子协程完成
      public async ETTask WaitAsync()
      {
        if (this.count <= 0)
        {
          return; // 如果没有任务需要等待，直接返回
        }
        this.tcs = ETTask.Create(true); // 创建一个新的ETTask用于等待
        await tcs; // 等待所有任务完成
      }
    }

    // 等待任意一个任务完成（List版本）
    public static async ETTask WaitAny(List<ETTask> tasks)
    {
      if (tasks.Count == 0)
      {
        return; // 如果任务列表为空，直接返回
      }

      CoroutineBlocker coroutineBlocker = new CoroutineBlocker(1); // 创建一个协程阻塞器

      foreach (ETTask task in tasks)
      {
        coroutineBlocker.RunSubCoroutineAsync(task).Coroutine(); // 运行每个子协程
      }

      await coroutineBlocker.WaitAsync(); // 等待任意一个任务完成
    }

    // 等待任意一个任务完成（数组版本）
    public static async ETTask WaitAny(ETTask[] tasks)
    {
      if (tasks.Length == 0)
      {
        return; // 如果任务数组为空，直接返回
      }

      CoroutineBlocker coroutineBlocker = new CoroutineBlocker(1); // 创建一个协程阻塞器

      foreach (ETTask task in tasks)
      {
        coroutineBlocker.RunSubCoroutineAsync(task).Coroutine(); // 运行每个子协程
      }

      await coroutineBlocker.WaitAsync(); // 等待任意一个任务完成
    }

    // 等待所有任务完成（数组版本）
    public static async ETTask WaitAll(ETTask[] tasks)
    {
      if (tasks.Length == 0)
      {
        return; // 如果任务数组为空，直接返回
      }

      CoroutineBlocker coroutineBlocker = new CoroutineBlocker(tasks.Length); // 创建一个协程阻塞器

      foreach (ETTask task in tasks)
      {
        coroutineBlocker.RunSubCoroutineAsync(task).Coroutine(); // 运行每个子协程
      }

      await coroutineBlocker.WaitAsync(); // 等待所有任务完成
    }

    // 等待所有任务完成（List版本）
    public static async ETTask WaitAll(List<ETTask> tasks)
    {
      if (tasks.Count == 0)
      {
        return; // 如果任务列表为空，直接返回
      }

      CoroutineBlocker coroutineBlocker = new CoroutineBlocker(tasks.Count); // 创建一个协程阻塞器

      foreach (ETTask task in tasks)
      {
        coroutineBlocker.RunSubCoroutineAsync(task).Coroutine(); // 运行每个子协程
      }

      await coroutineBlocker.WaitAsync(); // 等待所有任务完成
    }
  }
}