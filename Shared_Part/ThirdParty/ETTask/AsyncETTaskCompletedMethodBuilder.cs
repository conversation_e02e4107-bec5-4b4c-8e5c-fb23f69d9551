﻿using System;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using System.Security;

namespace MaoYouJi
{
  // 定义一个结构体 AsyncETTaskCompletedMethodBuilder
  public struct AsyncETTaskCompletedMethodBuilder
  {
    // 1. 静态 Create 方法，用于创建 AsyncETTaskCompletedMethodBuilder 的实例
    [DebuggerHidden]
    public static AsyncETTaskCompletedMethodBuilder Create()
    {
      AsyncETTaskCompletedMethodBuilder builder = new();
      return builder;
    }

    // 2. Task 属性，返回一个默认的 ETTaskCompleted 实例
    public ETTaskCompleted Task => default;

    // 3. SetException 方法，用于处理异常
    [DebuggerHidden]
    public void SetException(Exception e)
    {
      // 调用 ETTask 的异常处理程序
      ETTask.ExceptionHandler.Invoke(e);
    }

    // 4. SetResult 方法，设置任务结果（此处不执行任何操作）
    [DebuggerHidden]
    public void SetResult()
    {
      // do nothing
    }

    // 5. AwaitOnCompleted 方法，用于处理 awaiter 和状态机的完成
    [DebuggerHidden]
    public void AwaitOnCompleted<TAwaiter, TStateMachine>(ref TAwaiter awaiter, ref TStateMachine stateMachine) where TAwaiter : INotifyCompletion where TStateMachine : IAsyncStateMachine
    {
      // awaiter.OnCompleted(stateMachine.MoveNext);
    }

    // 6. AwaitUnsafeOnCompleted 方法，类似于 AwaitOnCompleted，但用于不安全的上下文
    [DebuggerHidden]
    [SecuritySafeCritical]
    public void AwaitUnsafeOnCompleted<TAwaiter, TStateMachine>(ref TAwaiter awaiter, ref TStateMachine stateMachine) where TAwaiter : ICriticalNotifyCompletion where TStateMachine : IAsyncStateMachine
    {
      // awaiter.UnsafeOnCompleted(stateMachine.MoveNext);
    }

    // 7. Start 方法
    [DebuggerHidden]
    public void Start<TStateMachine>(ref TStateMachine stateMachine) where TStateMachine : IAsyncStateMachine
    {
      // stateMachine.MoveNext();
    }

    // 8. SetStateMachine 方法，设置状态机
    [DebuggerHidden]
    public void SetStateMachine(IAsyncStateMachine stateMachine)
    {
    }
  }
}