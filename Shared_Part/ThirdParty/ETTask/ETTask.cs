﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using System.Runtime.ExceptionServices;

namespace MaoYouJi
{
  // ETTask类实现了ICriticalNotifyCompletion接口，用于异步任务的管理
  [AsyncMethodBuilder(typeof(ETAsyncTaskMethodBuilder))]
  public class ETTask : ICriticalNotifyCompletion
  {
    // 异常处理器
    public static Action<Exception> ExceptionHandler;

    // 返回一个已完成的ETTask
    public static ETTaskCompleted CompletedTask
    {
      get
      {
        return new ETTaskCompleted();
      }
    }

    // 任务对象池
    private static readonly ConcurrentQueue<ETTask> queue = new();

    /// <summary>
    /// 创建一个ETTask实例，可以选择从对象池中获取
    /// </summary>
    public static ETTask Create(bool fromPool = false)
    {
      if (!fromPool)
      {
        return new ETTask();
      }
      if (!queue.TryDequeue(out ETTask task))
      {
        return new ETTask() { fromPool = true };
      }
      return task;
    }

    // 回收任务到对象池
    private void Recycle()
    {
      if (!this.fromPool)
      {
        return;
      }

      this.state = AwaiterStatus.Pending;
      this.callback = null;
      // 如果队列中任务过多则不回收
      if (queue.Count > 1000)
      {
        return;
      }
      queue.Enqueue(this);
    }

    private bool fromPool; // 是否来自对象池
    private AwaiterStatus state; // 任务状态
    private object callback; // 回调函数或异常信息

    private ETTask()
    {
    }

    // 内部协程方法
    [DebuggerHidden]
    private async ETVoid InnerCoroutine()
    {
      await this;
    }

    // 启动协程
    [DebuggerHidden]
    public void Coroutine()
    {
      InnerCoroutine().Coroutine();
    }

    // 获取当前任务的等待者
    [DebuggerHidden]
    public ETTask GetAwaiter()
    {
      return this;
    }

    // 判断任务是否完成
    public bool IsCompleted
    {
      [DebuggerHidden]
      get
      {
        return this.state != AwaiterStatus.Pending;
      }
    }

    // 设置任务完成后的回调
    [DebuggerHidden]
    public void UnsafeOnCompleted(Action action)
    {
      if (this.state != AwaiterStatus.Pending)
      {
        action?.Invoke();
        return;
      }

      this.callback = action;
    }

    // 设置任务完成后的回调
    [DebuggerHidden]
    public void OnCompleted(Action action)
    {
      this.UnsafeOnCompleted(action);
    }

    // 获取任务结果
    [DebuggerHidden]
    public void GetResult()
    {
      switch (this.state)
      {
        case AwaiterStatus.Succeeded:
          this.Recycle();
          break;
        case AwaiterStatus.Faulted:
          ExceptionDispatchInfo c = this.callback as ExceptionDispatchInfo;
          this.callback = null;
          this.Recycle();
          c?.Throw();
          break;
        default:
          throw new NotSupportedException("ETTask does not allow call GetResult directly when task not completed. Please use 'await'.");
      }
    }

    // 设置任务成功完成
    [DebuggerHidden]
    public void SetResult()
    {
      if (this.state != AwaiterStatus.Pending)
      {
        throw new InvalidOperationException("TaskT_TransitionToFinal_AlreadyCompleted");
      }

      this.state = AwaiterStatus.Succeeded;

      Action c = this.callback as Action;
      this.callback = null;
      c?.Invoke();
    }

    // 设置任务异常
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    [DebuggerHidden]
    public void SetException(Exception e)
    {
      if (this.state != AwaiterStatus.Pending)
      {
        throw new InvalidOperationException("TaskT_TransitionToFinal_AlreadyCompleted");
      }

      this.state = AwaiterStatus.Faulted;

      Action c = this.callback as Action;
      this.callback = ExceptionDispatchInfo.Capture(e);
      c?.Invoke();
    }
  }

  // 泛型版本的ETTask类
  [AsyncMethodBuilder(typeof(ETAsyncTaskMethodBuilder<>))]
  public class ETTask<T> : ICriticalNotifyCompletion
  {
    private static readonly ConcurrentQueue<ETTask<T>> queue = new();

    /// <summary>
    /// 创建一个ETTask<T>实例，可以选择从对象池中获取
    /// </summary>
    public static ETTask<T> Create(bool fromPool = false)
    {
      if (!fromPool)
      {
        return new ETTask<T>();
      }

      if (!queue.TryDequeue(out ETTask<T> task))
      {
        return new ETTask<T>() { fromPool = true };
      }
      return task;
    }

    // 回收任务到对象池
    private void Recycle()
    {
      if (!this.fromPool)
      {
        return;
      }
      this.callback = null;
      this.value = default;
      this.state = AwaiterStatus.Pending;
      // 如果队列中任务过多则不回收
      if (queue.Count > 1000)
      {
        return;
      }
      queue.Enqueue(this);
    }

    private bool fromPool; // 是否来自对象池
    private AwaiterStatus state; // 任务状态
    private T value; // 任务结果
    private object callback; // 回调函数或异常信息

    private ETTask()
    {
    }

    // 内部协程方法
    [DebuggerHidden]
    private async ETVoid InnerCoroutine()
    {
      await this;
    }

    // 启动协程
    [DebuggerHidden]
    public void Coroutine()
    {
      InnerCoroutine().Coroutine();
    }

    // 获取当前任务的等待者
    [DebuggerHidden]
    public ETTask<T> GetAwaiter()
    {
      return this;
    }

    // 获取任务结果
    [DebuggerHidden]
    public T GetResult()
    {
      switch (this.state)
      {
        case AwaiterStatus.Succeeded:
          T v = this.value;
          this.Recycle();
          return v;
        case AwaiterStatus.Faulted:
          ExceptionDispatchInfo c = this.callback as ExceptionDispatchInfo;
          this.callback = null;
          this.Recycle();
          c?.Throw();
          return default;
        default:
          throw new NotSupportedException("ETask does not allow call GetResult directly when task not completed. Please use 'await'.");
      }
    }

    // 判断任务是否完成
    public bool IsCompleted
    {
      [DebuggerHidden]
      get
      {
        return state != AwaiterStatus.Pending;
      }
    }

    // 设置任务完成后的回调
    [DebuggerHidden]
    public void UnsafeOnCompleted(Action action)
    {
      if (this.state != AwaiterStatus.Pending)
      {
        action?.Invoke();
        return;
      }

      this.callback = action;
    }

    // 设置任务完成后的回调
    [DebuggerHidden]
    public void OnCompleted(Action action)
    {
      this.UnsafeOnCompleted(action);
    }

    // 设置任务成功完成
    [DebuggerHidden]
    public void SetResult(T result)
    {
      if (this.state != AwaiterStatus.Pending)
      {
        throw new InvalidOperationException("TaskT_TransitionToFinal_AlreadyCompleted");
      }

      this.state = AwaiterStatus.Succeeded;

      this.value = result;

      Action c = this.callback as Action;
      this.callback = null;
      c?.Invoke();
    }

    // 设置任务异常
    [DebuggerHidden]
    public void SetException(Exception e)
    {
      if (this.state != AwaiterStatus.Pending)
      {
        throw new InvalidOperationException("TaskT_TransitionToFinal_AlreadyCompleted");
      }

      this.state = AwaiterStatus.Faulted;

      Action c = this.callback as Action;
      this.callback = ExceptionDispatchInfo.Capture(e);
      c?.Invoke();
    }
  }
}