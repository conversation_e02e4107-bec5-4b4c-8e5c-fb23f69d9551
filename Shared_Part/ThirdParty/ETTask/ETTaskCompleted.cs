﻿using System;
using System.Diagnostics;
using System.Runtime.CompilerServices;

namespace MaoYouJi
{
  // 使用自定义的异步方法构建器
  [AsyncMethodBuilder(typeof(AsyncETTaskCompletedMethodBuilder))]
  // 定义一个结构体，表示一个已经完成的任务
  public struct ETTaskCompleted : ICriticalNotifyCompletion
  {
    // 返回当前实例作为等待者
    [DebuggerHidden]
    public ETTaskCompleted GetAwaiter()
    {
      return this;
    }

    // 表示任务是否已经完成，始终返回true
    [DebuggerHidden]
    public bool IsCompleted => true;

    // 获取任务结果的方法，这里不做任何操作
    [DebuggerHidden]
    public void GetResult()
    {
    }

    // 当任务完成时调用的回调，这里不做任何操作
    [DebuggerHidden]
    public void OnCompleted(Action continuation)
    {
    }

    // 不安全的完成回调，这里不做任何操作
    [DebuggerHidden]
    public void UnsafeOnCompleted(Action continuation)
    {
    }
  }
}