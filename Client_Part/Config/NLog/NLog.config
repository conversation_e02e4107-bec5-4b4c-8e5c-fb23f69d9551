﻿<?xml version="1.0" encoding="utf-8" ?>

<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<targets async="true">
		<!-- 注意这里并没有开启bufferSize，上线要关闭Debug日志 -->
		<target name="ServerDebug" xsi:type="File"
				openFileCacheTimeout="30"
				keepFileOpen="true"
				fileName="${basedir}/../Logs/${logger}.${date:format=yyyyMMddHH}.Debug.log"
				deleteOldFileOnStartup="false"
				layout="${longdate} ${callsite:className=false:methodName=false:fileName=true:includeSourcePath=false:skipFrames=2} ${message}" />
	</targets>

	<targets async="true">
		<target name="ServerInfo" xsi:type="File"
				bufferSize="10240"
				openFileCacheTimeout="30"
				keepFileOpen="true"
				fileName="${basedir}/../Logs/${logger}.${date:format=yyyyMMddHH}.Info.log"
				deleteOldFileOnStartup="false"
				layout="${longdate} ${message}" />
	</targets>

	<targets async="true">
		<target name="ServerWarn" xsi:type="File"
				bufferSize="10240"
				openFileCacheTimeout="30"
				keepFileOpen="true"
				fileName="${basedir}/../Logs/${logger}.${date:format=yyyyMMddHH}.Warn.log"
				deleteOldFileOnStartup="false"
				layout="${longdate} ${message}" />
	</targets>

	<targets async="true">
		<target name="ServerError" xsi:type="File"
				openFileCacheTimeout="10"
				keepFileOpen="true"
				fileName="${basedir}/../Logs/${logger}.${date:format=yyyyMMddHH}.Error.log"
				deleteOldFileOnStartup="false"
				layout="${longdate} ${callsite:className=false:methodName=false:fileName=true:includeSourcePath=false:skipFrames=2} ${message}" />
	</targets>

	<targets async="true">
		<target name="ErrorConsole" xsi:type="Console" layout="${longdate} ${message}" />
	</targets>

	<rules>
		<!-- 修改日志规则，避免重复记录 -->
		<!-- 只在 Debug 文件中记录 Trace 和 Debug 级别的日志 -->
		<logger ruleName="ServerDebug" name="*" minlevel="Trace" maxlevel="Debug" writeTo="ServerDebug" final="true" />
		<!-- 只在 Info 文件中记录 Info 级别的日志 -->
		<logger ruleName="ServerInfo" name="*" minlevel="Info" maxlevel="Info" writeTo="ServerInfo" final="true" />
		<!-- 只在 Warn 文件中记录 Warn 级别的日志 -->
		<logger ruleName="ServerWarn" name="*" minlevel="Warn" maxlevel="Warn" writeTo="ServerWarn" final="true" />
		<!-- 只在 Error 文件中记录 Error 级别的日志 -->
		<logger ruleName="ServerError" name="*" minlevel="Error" maxlevel="Error" writeTo="ServerError" final="true" />
		<!-- <logger ruleName="ErrorConsole" name="*" minlevel="Warn" maxlevel="Error" writeTo="ErrorConsole" /> -->
	</rules>
</nlog>