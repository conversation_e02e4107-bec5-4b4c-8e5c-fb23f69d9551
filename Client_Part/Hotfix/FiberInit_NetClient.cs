﻿namespace MaoYouJi
{
  [Invoke((long)SceneType.NetClient)]
  public class FiberInit_NetClient : AInvokeHandler<FiberInit, ETTask>
  {
    public override async ETTask Handle(FiberInit fiberInit)
    {
      ETLog.Info("FiberInit_NetClient");
      Scene root = fiberInit.Fiber.Root;
      root.AddComponent<MailBoxComponent, MailBoxType>(MailBoxType.UnOrderedMessage);
      root.AddComponent<TimerComponent>();
      root.AddComponent<CoroutineLockComponent>();
      root.AddComponent<ProcessInnerSender>();
      root.AddComponent<FiberParentComponent>();
      await ETTask.CompletedTask;
    }
  }
}