using System.Threading.Tasks;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(ClientSenderComponent))]
  [FriendOf(typeof(ClientSenderComponent))]
  public static partial class ClientSenderComponentSystem
  {
    [EntitySystem]
    private static void Awake(this ClientSenderComponent self)
    {

    }

    [EntitySystem]
    private static void Destroy(this ClientSenderComponent self)
    {
      self.RemoveFiberAsync().Coroutine();
    }

    private static async ETTask RemoveFiberAsync(this ClientSenderComponent self)
    {
      if (self.fiberId == 0)
      {
        return;
      }

      int fiberId = self.fiberId;
      self.fiberId = 0;
      await FiberManager.Instance.Remove(fiberId);
    }

    public static async ETTask DisposeAsync(this ClientSenderComponent self)
    {
      await self.RemoveFiberAsync();
      self.Dispose();
    }

    // public static async ETTask<long> LoginAsync(this ClientSenderComponent self, string account, string password)
    // {
    //   self.fiberId = await FiberManager.Instance.Create(SchedulerType.ThreadPool, 0, SceneType.NetClient, "");
    //   self.netClientActorId = new ActorId(self.Fiber().Process, self.fiberId);

    //   Main2NetClient_Login main2NetClientLogin = Main2NetClient_Login.Create();
    //   main2NetClientLogin.OwnerFiberId = self.Fiber().Id;
    //   main2NetClientLogin.Account = account;
    //   main2NetClientLogin.Password = password;
    //   ETLog.Debug($"main2NetClientLogin: {account} {password}");
    //   NetClient2Main_Login response = await self.Root().GetComponent<ProcessInnerSender>().Call(self.netClientActorId, main2NetClientLogin) as NetClient2Main_Login;
    //   return response.PlayerId;
    // }

    public static async ETTask<TapTapLogInResp> TapTapLoginAsync(this ClientSenderComponent self, TapTapAccessToken tapTapAccessToken)
    {
      if (self.fiberId == 0)
      {
        self.fiberId = await FiberManager.Instance.Create(SchedulerType.ThreadPool, 0, SceneType.NetClient, "");
        self.netClientActorId = new ActorId(self.Fiber().Process, self.fiberId);
      }

      TapTapLogInReq tapTapLogInIn = new()
      {
        OwnerFiberId = self.Fiber().Id,
        tapTapAccessToken = tapTapAccessToken
      };
      ETLog.Debug($"main2NetClientLogin: {tapTapAccessToken.openId} {tapTapAccessToken.unionId}");
      TapTapLogInResp response = await self.Root().GetComponent<ProcessInnerSender>().Call(self.netClientActorId, tapTapLogInIn) as TapTapLogInResp;
      return response;
    }

    public static void Send(this ClientSenderComponent self, IMessage message)
    {
      A2NetClient_Message a2NetClientMessage = A2NetClient_Message.Create();
      a2NetClientMessage.MessageObject = message;
      self.Root().GetComponent<ProcessInnerSender>().Send(self.netClientActorId, a2NetClientMessage);
    }

    public static async ETTask<IResponse> Call(this ClientSenderComponent self, IRequest request, bool needException = true)
    {
      A2NetClient_Request a2NetClientRequest = A2NetClient_Request.Create();
      a2NetClientRequest.MessageObject = request;
      ETLog.Info($"Call: {request}");
      using A2NetClient_Response a2NetClientResponse = await self.Root().GetComponent<ProcessInnerSender>().Call(self.netClientActorId, a2NetClientRequest) as A2NetClient_Response;
      IResponse response = a2NetClientResponse.MessageObject;

      if (response.Error == ErrorCore.ERR_MessageTimeout)
      {
        throw new RpcException(response.Error, $"Rpc error: request, 注意Actor消息超时，请注意查看是否死锁或者没有reply: {request}, response: {response}");
      }

      if (needException && ErrorCore.IsRpcNeedThrowException(response.Error))
      {
        throw new RpcException(response.Error, $"Rpc error: {request}, response: {response}");
      }
      return response;
    }

  }
}