using System.Threading.Tasks;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(ShowUserComponent))]
  [FriendOf(typeof(ShowUserComponent))]
  public static partial class ShowUserComponentSystem
  {
    [EntitySystem]
    private static void Awake(this ShowUserComponent self)
    {

    }
  }

  [EntitySystemOf(typeof(ShowMapComponent))]
  [FriendOf(typeof(ShowMapComponent))]
  public static partial class ShowMapComponentSystem
  {
    [EntitySystem]
    private static void Awake(this ShowMapComponent self)
    {

    }
  }

  [EntitySystemOf(typeof(ShowMonComponent))]
  [FriendOf(typeof(ShowMonComponent))]
  public static partial class ShowMonComponentSystem
  {
    [EntitySystem]
    private static void Awake(this ShowMonComponent self)
    {

    }
  }

  [EntitySystemOf(typeof(ShowNpcComponent))]
  [FriendOf(typeof(ShowNpcComponent))]
  public static partial class ShowNpcComponentSystem
  {
    [EntitySystem]
    private static void Awake(this ShowNpcComponent self)
    {
    }
  }
}