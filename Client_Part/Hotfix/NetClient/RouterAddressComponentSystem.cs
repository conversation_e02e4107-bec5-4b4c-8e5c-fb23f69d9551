﻿using System;
using System.IO;
using System.Net;
using System.Net.Sockets;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(RouterAddressComponent))]
  [FriendOf(typeof(RouterAddressComponent))]
  public static partial class RouterAddressComponentSystem
  {
    [EntitySystem]
    private static void Awake(this RouterAddressComponent self, string address, int port)
    {
      self.RouterManagerHost = address;
      self.RouterManagerPort = port;
    }

    public static async ETTask Init(this RouterAddressComponent self)
    {
      self.RouterManagerIPAddress = NetworkHelper.GetHostAddress(self.RouterManagerHost);
      await self.GetAllRouter();
    }

    private static async ETTask GetAllRouter(this RouterAddressComponent self)
    {
      string url = $"http://{self.RouterManagerHost}:{self.RouterManagerPort}/get_router?v={RandomGenerator.RandUInt32()}";
      ETLog.Debug($"start get router info: {url}");
      string routerInfo = await HttpClientHelper.Get(url);
      ETLog.Debug($"recv router info: {routerInfo}");
      HttpGetRouterResponse httpGetRouterResponse = MongoHelper.FromJson<HttpGetRouterResponse>(routerInfo);
      self.Info = httpGetRouterResponse;
      ETLog.Debug($"start get router info finish: {MongoHelper.ToJson(httpGetRouterResponse)}");

      // 打乱顺序
      RandomGenerator.BreakRank(self.Info.Routers);

      self.WaitTenMinGetAllRouter().Coroutine();
    }

    // 等10分钟再获取一次
    public static async ETTask WaitTenMinGetAllRouter(this RouterAddressComponent self)
    {
      await self.Root().GetComponent<TimerComponent>().WaitAsync(5 * 60 * 1000);
      if (self.IsDisposed)
      {
        return;
      }
      await self.GetAllRouter();
    }

    public static IPEndPoint GetAddress(this RouterAddressComponent self)
    {
      if (self.Info.Routers.Count == 0)
      {
        return null;
      }

      string address = self.Info.Routers[self.RouterIndex++ % self.Info.Routers.Count];
      ETLog.Info($"get router address: {self.RouterIndex - 1} {address}");
      string[] ss = address.Split(':');
      IPAddress ipAddress = IPAddress.Parse(ss[0]);
      if (self.RouterManagerIPAddress.AddressFamily == AddressFamily.InterNetworkV6)
      {
        ipAddress = ipAddress.MapToIPv6();
      }
      return new IPEndPoint(ipAddress, int.Parse(ss[1]));
    }

    public static IPEndPoint GetRealmAddress(this RouterAddressComponent self)
    {
      int v = (int)(RandomGenerator.RandUInt32() % self.Info.Realms.Count);
      string address = self.Info.Realms[v];
      string[] ss = address.Split(':');
      IPAddress ipAddress = IPAddress.Parse(ss[0]);
      // if (self.IPAddress.AddressFamily == AddressFamily.InterNetworkV6)
      // { 
      //    ipAddress = ipAddress.MapToIPv6();
      // }
      return new IPEndPoint(ipAddress, int.Parse(ss[1]));
    }

    public static IPEndPoint GetGateAddress(this RouterAddressComponent self, int ZoneId, long netAccountId)
    {
      if (!self.Info.Gates.ContainsKey(ZoneId))
      {
        return null;
      }

      int v = (int)(netAccountId % self.Info.Gates[ZoneId].Count);
      string address = self.Info.Gates[ZoneId][v];
      string[] ss = address.Split(':');
      return new IPEndPoint(IPAddress.Parse(ss[0]), int.Parse(ss[1]));
    }
  }
}