using System;
using System.Net;
using System.Net.Sockets;

namespace Mao<PERSON><PERSON>Ji
{
  [MessageHandler(SceneType.Main)]
  public class MainHander_NetMsg : MessageHandler<Scene, NetClient2Main_NewMsg>
  {
    protected override async ETTask Run(Scene root, NetClient2Main_NewMsg message)
    {
      try
      {
        ETRpcCall.InvokeMsgHandler(message.MessageObject);
      }
      catch (Exception e)
      {
        ETLog.Error(e);
      }
      await ETTask.CompletedTask;
    }
  }
}