<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        <AssemblyName>Core</AssemblyName>
        <RootNamespace>MaoYouJi</RootNamespace>
        <LangVersion>12</LangVersion>
    </PropertyGroup>

    <PropertyGroup>
        <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
        <SatelliteResourceLanguages>en</SatelliteResourceLanguages>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
      <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
      <DefineConstants>DOTNET</DefineConstants>
      <OutputPath>..\..\Bin\</OutputPath>
      <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
      <Optimize>false</Optimize>
      <NoWarn>0169,0649,3021,8981,CS9193,CS9192,NU1903</NoWarn>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">  
      <AllowUnsafeBlocks>true</AllowUnsafeBlocks> 
      <DefineConstants>DOTNET</DefineConstants>
      <OutputPath>..\..\Bin\</OutputPath>
      <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
      <NoWarn>0169,0649,3021,8981,CS9193,CS9192,NU1903</NoWarn>
    </PropertyGroup>
    
    <ItemGroup> 
        <Compile Include="..\Shared_Part\Core\**\*.cs">
            <Link>Share\%(RecursiveDir)%(FileName)%(Extension)</Link> 
        </Compile> 
        <Compile Include="..\Shared_Part\Hotfix\**\*.cs">
            <Link>Share\%(RecursiveDir)%(FileName)%(Extension)</Link> 
        </Compile> 
        <Compile Include="..\Shared_Part\Model\**\*.cs">
            <Link>Share\%(RecursiveDir)%(FileName)%(Extension)</Link> 
        </Compile> 
    </ItemGroup>
    
    <ItemGroup>
      <ProjectReference Include="..\Server_Part\Share\Analyzer\Share.Analyzer.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false" /> 
      <ProjectReference Include="..\Server_Part\Share\Share.SourceGenerator\Share.SourceGenerator.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false" />
      <ProjectReference Include="..\Server_Part\DotNet\ThirdParty\DotNet.ThirdParty.csproj" />
      <PackageReference Include="ConcurrentHashSet" Version="1.3.0" />
    </ItemGroup>

</Project>
