using System;
using System.Collections.Generic;
using System.IO;

namespace MaoYouJi
{
  public static class ConfigPathInfo
  {
    [StaticField]
    public static string ConfigPath = "./maoyouji2024/Assets/GameMain/Scripts/ET_Client_Part/Config";
  }

  [Invoke]
  public class GetAllConfigBytes : AInvokeHandler<ConfigLoader.GetAllConfigBytes, ETTask<Dictionary<Type, byte[]>>>
  {
    public override async ETTask<Dictionary<Type, byte[]>> Handle(ConfigLoader.GetAllConfigBytes args)
    {
      Dictionary<Type, byte[]> output = new Dictionary<Type, byte[]>();
      List<string> startConfigs = new List<string>()
            {
                "StartMachineConfigCategory",
                "StartProcessConfigCategory",
                "StartSceneConfigCategory",
                "StartZoneConfigCategory",
            };
      HashSet<Type> configTypes = CodeTypes.Instance.GetTypes(typeof(ConfigAttribute));
      foreach (Type configType in configTypes)
      {
        string configFilePath;
        if (startConfigs.Contains(configType.Name))
        {
          configFilePath = $"{ConfigPathInfo.ConfigPath}/Excel/c/{Options.Instance.StartConfig}/{configType.Name}.bytes";
        }
        else
        {
          configFilePath = $"{ConfigPathInfo.ConfigPath}/Excel/c/{configType.Name}.bytes";
        }
        output[configType] = File.ReadAllBytes(configFilePath);
      }

      await ETTask.CompletedTask;
      return output;
    }
  }

  [Invoke]
  public class GetAllConfigJson : AInvokeHandler<ConfigLoader.GetAllConfigJson, ETTask<Dictionary<Type, string>>>
  {
    public override async ETTask<Dictionary<Type, string>> Handle(ConfigLoader.GetAllConfigJson args)
    {
      Dictionary<Type, string> output = new Dictionary<Type, string>();
      List<string> startConfigs = new List<string>()
            {
                "StartMachineConfigCategory",
                "StartProcessConfigCategory",
                "StartSceneConfigCategory",
                "StartZoneConfigCategory",
            };
      HashSet<Type> configTypes = CodeTypes.Instance.GetTypes(typeof(ConfigAttribute));
      ETLog.Info($"configTypes: {configTypes.Count}");
      foreach (Type configType in configTypes)
      {
        ETLog.Info($"configType: {configType}");
        string configFilePath;
        if (startConfigs.Contains(configType.Name))
        {
          configFilePath = $"{ConfigPathInfo.ConfigPath}/Json/c/{Options.Instance.StartConfig}/{configType.Name}.json";
        }
        else
        {
          configFilePath = $"{ConfigPathInfo.ConfigPath}/Json/c/{configType.Name}.json";
        }
        output[configType] = File.ReadAllText(configFilePath);
      }

      await ETTask.CompletedTask;
      return output;
    }
  }

  [Invoke]
  public class GetOneConfigBytes : AInvokeHandler<ConfigLoader.GetOneConfigBytes, byte[]>
  {
    public override byte[] Handle(ConfigLoader.GetOneConfigBytes args)
    {
      byte[] configBytes = File.ReadAllBytes($"{ConfigPathInfo.ConfigPath}/Excel/c/{args.ConfigName}.bytes");
      return configBytes;
    }
  }
}